@echo off
chcp 65001 >nul
echo ========================================
echo    JiangJiangNote 开发环境启动脚本
echo ========================================
echo.

:: 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Go环境，请先安装Go 1.24+
    pause
    exit /b 1
)

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js环境，请先安装Node.js 18+
    pause
    exit /b 1
)

:: 检查项目结构
if not exist "backend" (
    echo [错误] 未找到backend目录，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "frontend" (
    echo [错误] 未找到frontend目录，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo [信息] 正在检查环境配置...

:: 检查.env文件
if not exist ".env" (
    if exist ".env.example" (
        echo [信息] 复制环境配置文件...
        copy ".env.example" ".env" >nul
        echo [提示] 请根据需要修改.env文件中的配置
    ) else (
        echo [警告] 未找到环境配置文件
    )
)

:: 启动数据库服务
echo [信息] 启动数据库服务...
docker-compose up -d postgres minio redis 2>nul
if %errorlevel% neq 0 (
    echo [警告] Docker服务启动失败，请确保Docker Desktop已启动
    echo [提示] 或者手动启动PostgreSQL和MinIO服务
)

:: 安装后端依赖
echo [信息] 安装后端依赖...
cd backend
go mod tidy
if %errorlevel% neq 0 (
    echo [错误] 后端依赖安装失败
    cd ..
    pause
    exit /b 1
)
cd ..

:: 安装前端依赖
echo [信息] 检查前端依赖...
cd frontend
if not exist "node_modules" (
    echo [信息] 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
)
cd ..

echo.
echo [信息] 环境准备完成，正在启动服务...
echo.

:: 启动后端服务
echo [信息] 启动后端服务...
start "JiangJiangNote Backend" cmd /k "cd backend && go run cmd/server/main.go"

:: 等待后端服务启动
timeout /t 3 /nobreak >nul

:: 启动前端服务
echo [信息] 启动前端服务...
start "JiangJiangNote Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo    JiangJiangNote 开发服务器已启动
echo ========================================
echo.
echo 后端API服务: http://localhost:8080
echo 前端应用:   http://localhost:3000
echo.
echo 数据库管理:
echo - PostgreSQL: localhost:5432
echo - MinIO控制台: http://localhost:9001
echo.
echo 按任意键关闭此窗口...
pause >nul
