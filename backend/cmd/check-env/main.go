package main

import (
	"fmt"
	"log"

	"jiangjiangNote/backend/internal/config"
	"jiangjiangNote/backend/internal/utils"
)

func main() {
	fmt.Println("========================================")
	fmt.Println("    JiangJiangNote 环境变量检查工具")
	fmt.Println("========================================")
	fmt.Println()

	// 加载环境变量
	fmt.Println("正在加载环境变量...")
	utils.LoadEnv()
	fmt.Println()

	// 打印环境变量状态
	utils.PrintEnvStatus()
	fmt.Println()

	// 加载配置
	fmt.Println("正在加载配置...")
	cfg := config.Load()
	fmt.Println()

	// 验证配置
	fmt.Println("=== 配置验证结果 ===")
	
	// 检查必要的配置项
	checks := []struct {
		name  string
		value string
		valid bool
	}{
		{"服务器端口", cfg.Server.Port, cfg.Server.Port != ""},
		{"数据库主机", cfg.Database.Host, cfg.Database.Host != ""},
		{"数据库名称", cfg.Database.DBName, cfg.Database.DBName != ""},
		{"数据库用户", cfg.Database.User, cfg.Database.User != ""},
		{"JWT密钥", cfg.JWT.Secret, cfg.JWT.Secret != "" && cfg.JWT.Secret != "your-secret-key"},
		{"MinIO端点", cfg.MinIO.Endpoint, cfg.MinIO.Endpoint != ""},
		{"MinIO存储桶", cfg.MinIO.BucketName, cfg.MinIO.BucketName != ""},
	}

	allValid := true
	for _, check := range checks {
		status := "✓"
		if !check.valid {
			status = "✗"
			allValid = false
		}
		
		// 隐藏敏感信息
		displayValue := check.value
		if check.name == "JWT密钥" {
			displayValue = "[HIDDEN]"
		}
		
		fmt.Printf("[%s] %s: %s\n", status, check.name, displayValue)
	}
	
	fmt.Println("========================")
	
	if allValid {
		fmt.Println("✅ 所有配置项验证通过！")
		log.Println("Configuration validation passed")
	} else {
		fmt.Println("❌ 部分配置项需要检查")
		log.Println("Some configuration items need attention")
	}
	
	fmt.Println()
	fmt.Println("检查完成。")
}
