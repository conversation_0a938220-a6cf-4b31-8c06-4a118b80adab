// Package main 是 JiangJiangNote 后端服务的入口点
// JiangJiangNote 是一个现代化的云笔记系统，支持 Markdown 和纯文本编辑
package main

import (
	"jiangjiangNote/backend/internal/utils"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"jiangjiangNote/backend/internal/api/routes"
	"jiangjiangNote/backend/internal/config"
	"jiangjiangNote/backend/internal/database"
)

// main 函数是应用程序的入口点
// 负责初始化配置、数据库、路由并启动HTTP服务器
func main() {
	log.Println("Starting JiangJiangNote Server...")

	// 加载环境变量配置文件 (.env)
	// 支持多路径查找，确保在不同环境下都能正确加载配置
	utils.LoadEnv()

	// 打印环境变量状态，用于调试和验证配置
	utils.PrintEnvStatus()

	// 初始化应用配置
	// 从环境变量中读取数据库、JWT、MinIO等配置信息
	cfg := config.Load()

	// 初始化数据库连接
	// 连接PostgreSQL数据库并验证连接
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 设置Gin框架的运行模式
	// production模式下会禁用调试信息，提高性能
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin HTTP引擎
	// Gin是一个高性能的HTTP Web框架
	r := gin.Default()

	// 设置API路由
	// 包括认证、文档管理、分类管理、搜索等功能路由
	routes.SetupRoutes(r, db)

	// 启动HTTP服务器
	// 优先使用环境变量PORT，否则使用配置文件中的端口
	port := os.Getenv("PORT")
	if port == "" {
		port = cfg.Server.Port
	}

	log.Printf("Server starting on port %s", port)
	log.Printf("API documentation available at http://localhost:%s/health", port)

	// 启动服务器，监听指定端口
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
