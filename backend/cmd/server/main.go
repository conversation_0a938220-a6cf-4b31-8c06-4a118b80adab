package main

import (
	"log"
	"os"

	"jiangjiangNote/backend/internal/api/routes"
	"jiangjiangNote/backend/internal/config"
	"jiangjiangNote/backend/internal/database"
	"jiangjiangNote/backend/internal/utils"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载环境变量 - 首先尝试从项目根目录加载
	if err := godotenv.Load("../../.env"); err != nil {
		// 如果根目录没有，尝试当前目录
		if err := godotenv.Load(".env"); err != nil {
			log.Println("Warning: No .env file found, using default values")
		}
	} else {
		log.Println("Successfully loaded .env file")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 设置Gin模式
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.Default()

	// 设置路由
	routes.SetupRoutes(r, db)

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = cfg.Server.Port
	}

	log.Printf("Server starting on port %s", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
