package config

import (
	"log"
	"os"
	"strconv"
)

type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	JWT      JWTConfig
	MinIO    MinIOConfig
	Redis    RedisConfig
}

type ServerConfig struct {
	Port string
	Mode string
}

type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	DBName   string
	SSLMode  string
}

type JWTConfig struct {
	Secret     string
	ExpireHour int
}

type MinIOConfig struct {
	Endpoint        string
	AccessKeyID     string
	SecretAccessKey string
	UseSSL          bool
	BucketName      string
}

type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

func Load() *Config {
	config := &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 5432),
			User:     getEnv("DB_USER", "cloudnotes"),
			Password: getEnv("DB_PASSWORD", "cloudnotes123"),
			DBName:   getEnv("DB_NAME", "cloudnotes"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		JWT: JWTConfig{
			Secret:     getEnv("JWT_SECRET", "jiangjiang-note-default-secret-key"),
			ExpireHour: getEnvAsInt("JWT_EXPIRE_HOUR", 24),
		},
		MinIO: MinIOConfig{
			Endpoint:        getEnv("MINIO_ENDPOINT", "localhost:9000"),
			AccessKeyID:     getEnv("MINIO_ACCESS_KEY", "minioadmin"),
			SecretAccessKey: getEnv("MINIO_SECRET_KEY", "minioadmin123"),
			UseSSL:          getEnvAsBool("MINIO_USE_SSL", false),
			BucketName:      getEnv("MINIO_BUCKET", "jiangjiangNote"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvAsInt("REDIS_DB", 0),
		},
	}

	// 打印配置信息（不包含敏感信息）
	logConfig(config)
	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func logConfig(config *Config) {
	log.Println("=== JiangJiangNote Configuration ===")
	log.Printf("Server Port: %s", config.Server.Port)
	log.Printf("Server Mode: %s", config.Server.Mode)
	log.Printf("Database Host: %s", config.Database.Host)
	log.Printf("Database Port: %d", config.Database.Port)
	log.Printf("Database Name: %s", config.Database.DBName)
	log.Printf("Database User: %s", config.Database.User)
	log.Printf("JWT Expire Hours: %d", config.JWT.ExpireHour)
	log.Printf("MinIO Endpoint: %s", config.MinIO.Endpoint)
	log.Printf("MinIO Bucket: %s", config.MinIO.BucketName)
	log.Printf("Redis Host: %s", config.Redis.Host)
	log.Printf("Redis Port: %s", config.Redis.Port)
	log.Println("=====================================")
}
