// Package config 提供应用程序的配置管理功能
// 支持从环境变量读取配置，并提供默认值
package config

import (
	"log"
	"os"
	"strconv"
)

// Config 应用程序总配置结构
type Config struct {
	Server   ServerConfig   // 服务器配置
	Database DatabaseConfig // 数据库配置
	JWT      JWTConfig      // JWT配置
	MinIO    MinIOConfig    // MinIO配置
	Redis    RedisConfig    // Redis配置
}

// ServerConfig 服务器相关配置
type ServerConfig struct {
	Port string // 服务器监听端口
	Mode string // 运行模式: debug, release, test
}

// DatabaseConfig 数据库连接配置
type DatabaseConfig struct {
	Host     string // 数据库主机地址
	Port     int    // 数据库端口
	User     string // 数据库用户名
	Password string // 数据库密码
	DBName   string // 数据库名称
	SSLMode  string // SSL连接模式
}

// JWTConfig JWT令牌配置
type JWTConfig struct {
	Secret     string // JWT签名密钥
	ExpireHour int    // 令牌过期时间（小时）
}

// MinIOConfig MinIO对象存储配置
type MinIOConfig struct {
	Endpoint        string // MinIO服务端点
	AccessKeyID     string // 访问密钥ID
	SecretAccessKey string // 秘密访问密钥
	UseSSL          bool   // 是否使用SSL
	BucketName      string // 存储桶名称
}

// RedisConfig Redis缓存配置
type RedisConfig struct {
	Host     string // Redis主机地址
	Port     string // Redis端口
	Password string // Redis密码
	DB       int    // Redis数据库编号
}

func Load() *Config {
	config := &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 5432),
			User:     getEnv("DB_USER", "cloudnotes"),
			Password: getEnv("DB_PASSWORD", "cloudnotes123"),
			DBName:   getEnv("DB_NAME", "cloudnotes"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		JWT: JWTConfig{
			Secret:     getEnv("JWT_SECRET", "jiangjiang-note-default-secret-key"),
			ExpireHour: getEnvAsInt("JWT_EXPIRE_HOUR", 24),
		},
		MinIO: MinIOConfig{
			Endpoint:        getEnv("MINIO_ENDPOINT", "localhost:9000"),
			AccessKeyID:     getEnv("MINIO_ACCESS_KEY", "minioadmin"),
			SecretAccessKey: getEnv("MINIO_SECRET_KEY", "minioadmin123"),
			UseSSL:          getEnvAsBool("MINIO_USE_SSL", false),
			BucketName:      getEnv("MINIO_BUCKET", "jiangjiangNote"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvAsInt("REDIS_DB", 0),
		},
	}

	// 打印配置信息（不包含敏感信息）
	logConfig(config)
	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func logConfig(config *Config) {
	log.Println("=== JiangJiangNote Configuration ===")
	log.Printf("Server Port: %s", config.Server.Port)
	log.Printf("Server Mode: %s", config.Server.Mode)
	log.Printf("Database Host: %s", config.Database.Host)
	log.Printf("Database Port: %d", config.Database.Port)
	log.Printf("Database Name: %s", config.Database.DBName)
	log.Printf("Database User: %s", config.Database.User)
	log.Printf("JWT Expire Hours: %d", config.JWT.ExpireHour)
	log.Printf("MinIO Endpoint: %s", config.MinIO.Endpoint)
	log.Printf("MinIO Bucket: %s", config.MinIO.BucketName)
	log.Printf("Redis Host: %s", config.Redis.Host)
	log.Printf("Redis Port: %s", config.Redis.Port)
	log.Println("=====================================")
}
