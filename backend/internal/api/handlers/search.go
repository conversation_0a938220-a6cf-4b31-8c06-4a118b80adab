package handlers

import (
	"net/http"
	"strings"

	"jiangjiangNote/backend/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type SearchHandler struct {
	db *gorm.DB
}

func NewSearchHandler(db *gorm.DB) *SearchHandler {
	return &SearchHandler{db: db}
}

// Search 搜索文档
func (h *SearchHandler) Search(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	query := strings.TrimSpace(c.Query("q"))
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	// 搜索文档
	var documents []models.Document
	searchPattern := "%" + query + "%"
	
	if err := h.db.Where("user_id = ? AND (title ILIKE ? OR content ILIKE ?)", 
		userID, searchPattern, searchPattern).
		Preload("Category").Preload("Tags").
		Order("updated_at DESC").
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Search failed"})
		return
	}

	// 搜索分类
	var categories []models.Category
	if err := h.db.Where("user_id = ? AND name ILIKE ?", userID, searchPattern).
		Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Category search failed"})
		return
	}

	// 搜索标签
	var tags []models.Tag
	if err := h.db.Where("user_id = ? AND name ILIKE ?", userID, searchPattern).
		Find(&tags).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Tag search failed"})
		return
	}

	// 转换为响应格式
	var documentResults []models.DocumentListResponse
	for _, doc := range documents {
		documentResults = append(documentResults, doc.ToListResponse())
	}

	var categoryResults []models.CategoryResponse
	for _, cat := range categories {
		categoryResults = append(categoryResults, cat.ToResponse())
	}

	var tagResults []models.TagResponse
	for _, tag := range tags {
		tagResults = append(tagResults, tag.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"query":      query,
		"documents":  documentResults,
		"categories": categoryResults,
		"tags":       tagResults,
		"total": gin.H{
			"documents":  len(documentResults),
			"categories": len(categoryResults),
			"tags":       len(tagResults),
		},
	})
}

// SearchDocuments 仅搜索文档
func (h *SearchHandler) SearchDocuments(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	query := strings.TrimSpace(c.Query("q"))
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	var documents []models.Document
	searchPattern := "%" + query + "%"
	
	if err := h.db.Where("user_id = ? AND (title ILIKE ? OR content ILIKE ?)", 
		userID, searchPattern, searchPattern).
		Preload("Category").Preload("Tags").
		Order("updated_at DESC").
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Search failed"})
		return
	}

	// 转换为响应格式
	var response []models.DocumentListResponse
	for _, doc := range documents {
		response = append(response, doc.ToListResponse())
	}

	c.JSON(http.StatusOK, response)
}
