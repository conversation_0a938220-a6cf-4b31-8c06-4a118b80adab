package handlers

import (
	"net/http"
	"strconv"

	"jiangjiangNote/backend/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TagHandler struct {
	db *gorm.DB
}

func NewTagHandler(db *gorm.DB) *TagHandler {
	return &TagHandler{db: db}
}

// GetTags 获取标签列表
func (h *TagHandler) GetTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var tags []models.Tag
	if err := h.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&tags).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch tags"})
		return
	}

	// 转换为响应格式并计算文档数量
	var response []models.TagResponse
	for _, tag := range tags {
		tagResp := tag.ToResponse()
		
		// 计算使用此标签的文档数量
		var docCount int64
		h.db.Table("document_tags").
			Joins("JOIN documents ON document_tags.document_id = documents.id").
			Where("document_tags.tag_id = ? AND documents.user_id = ? AND documents.deleted_at IS NULL", tag.ID, userID).
			Count(&docCount)
		
		tagResp.DocCount = docCount
		response = append(response, tagResp)
	}

	c.JSON(http.StatusOK, response)
}

// CreateTag 创建标签
func (h *TagHandler) CreateTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.TagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查标签名是否已存在
	var existingTag models.Tag
	if err := h.db.Where("user_id = ? AND name = ?", userID, req.Name).First(&existingTag).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Tag name already exists"})
		return
	}

	tag := models.Tag{
		UserID: userID.(uint),
		Name:   req.Name,
		Color:  req.Color,
	}

	if err := h.db.Create(&tag).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create tag"})
		return
	}

	c.JSON(http.StatusCreated, tag.ToResponse())
}

// UpdateTag 更新标签
func (h *TagHandler) UpdateTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID"})
		return
	}

	var req models.TagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var tag models.Tag
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&tag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Tag not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch tag"})
		}
		return
	}

	// 检查新标签名是否已存在（排除当前标签）
	var existingTag models.Tag
	if err := h.db.Where("user_id = ? AND name = ? AND id != ?", userID, req.Name, tag.ID).First(&existingTag).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Tag name already exists"})
		return
	}

	// 更新标签
	updates := map[string]interface{}{
		"name":  req.Name,
		"color": req.Color,
	}

	if err := h.db.Model(&tag).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update tag"})
		return
	}

	c.JSON(http.StatusOK, tag.ToResponse())
}

// DeleteTag 删除标签
func (h *TagHandler) DeleteTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID"})
		return
	}

	var tag models.Tag
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&tag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Tag not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch tag"})
		}
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除标签与文档的关联
	if err := tx.Where("tag_id = ?", tag.ID).Delete(&models.DocumentTag{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove tag associations"})
		return
	}

	// 删除标签
	if err := tx.Delete(&tag).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete tag"})
		return
	}

	tx.Commit()

	c.JSON(http.StatusOK, gin.H{"message": "Tag deleted successfully"})
}

// GetTagDocuments 获取标签下的文档
func (h *TagHandler) GetTagDocuments(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID"})
		return
	}

	// 验证标签是否存在且属于当前用户
	var tag models.Tag
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&tag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Tag not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch tag"})
		}
		return
	}

	// 获取标签下的文档
	var documents []models.Document
	if err := h.db.Table("documents").
		Joins("JOIN document_tags ON documents.id = document_tags.document_id").
		Where("document_tags.tag_id = ? AND documents.user_id = ?", id, userID).
		Preload("Category").Preload("Tags").
		Order("documents.updated_at DESC").
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch documents"})
		return
	}

	// 转换为列表响应格式
	var response []models.DocumentListResponse
	for _, doc := range documents {
		response = append(response, doc.ToListResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"tag":       tag.ToResponse(),
		"documents": response,
	})
}
