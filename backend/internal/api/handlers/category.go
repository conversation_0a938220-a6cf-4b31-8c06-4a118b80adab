package handlers

import (
	"net/http"
	"strconv"

	"jiangjiangNote/backend/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CategoryHandler struct {
	db *gorm.DB
}

func NewCategoryHandler(db *gorm.DB) *CategoryHandler {
	return &CategoryHandler{db: db}
}

// GetCategories 获取分类列表
func (h *CategoryHandler) GetCategories(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var categories []models.Category
	if err := h.db.Where("user_id = ?", userID).
		Preload("Children").
		Order("sort_order ASC, created_at ASC").
		Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	// 构建树形结构
	categoryMap := make(map[uint]*models.Category)
	var rootCategories []models.Category

	// 先将所有分类放入map
	for i := range categories {
		categoryMap[categories[i].ID] = &categories[i]
	}

	// 构建树形结构并计算文档数量
	for i := range categories {
		category := &categories[i]
		
		// 计算该分类下的文档数量
		var docCount int64
		h.db.Model(&models.Document{}).Where("category_id = ? AND user_id = ?", category.ID, userID).Count(&docCount)
		
		if category.ParentID == nil {
			// 根分类
			rootCategories = append(rootCategories, *category)
		} else {
			// 子分类，添加到父分类的Children中
			if parent, exists := categoryMap[*category.ParentID]; exists {
				parent.Children = append(parent.Children, *category)
			}
		}
	}

	// 转换为响应格式
	var response []models.CategoryResponse
	for _, category := range rootCategories {
		categoryResp := category.ToResponse()
		// 计算文档数量
		var docCount int64
		h.db.Model(&models.Document{}).Where("category_id = ? AND user_id = ?", category.ID, userID).Count(&docCount)
		categoryResp.DocCount = docCount
		response = append(response, categoryResp)
	}

	c.JSON(http.StatusOK, response)
}

// CreateCategory 创建分类
func (h *CategoryHandler) CreateCategory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.CategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 如果指定了父分类，验证父分类是否存在且属于当前用户
	if req.ParentID != nil {
		var parentCategory models.Category
		if err := h.db.Where("id = ? AND user_id = ?", *req.ParentID, userID).First(&parentCategory).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Parent category not found"})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate parent category"})
			}
			return
		}
	}

	category := models.Category{
		UserID:    userID.(uint),
		Name:      req.Name,
		Icon:      req.Icon,
		Color:     req.Color,
		ParentID:  req.ParentID,
		SortOrder: req.SortOrder,
	}

	if err := h.db.Create(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		return
	}

	// 重新加载分类以获取关联数据
	h.db.Where("id = ?", category.ID).Preload("Children").First(&category)

	c.JSON(http.StatusCreated, category.ToResponse())
}

// UpdateCategory 更新分类
func (h *CategoryHandler) UpdateCategory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req models.CategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var category models.Category
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch category"})
		}
		return
	}

	// 如果指定了父分类，验证父分类是否存在且不是自己或自己的子分类
	if req.ParentID != nil {
		if *req.ParentID == category.ID {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Category cannot be its own parent"})
			return
		}

		var parentCategory models.Category
		if err := h.db.Where("id = ? AND user_id = ?", *req.ParentID, userID).First(&parentCategory).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Parent category not found"})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate parent category"})
			}
			return
		}

		// 检查是否会形成循环引用
		if h.wouldCreateCycle(category.ID, *req.ParentID) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot create circular reference"})
			return
		}
	}

	// 更新分类
	updates := map[string]interface{}{
		"name":       req.Name,
		"icon":       req.Icon,
		"color":      req.Color,
		"parent_id":  req.ParentID,
		"sort_order": req.SortOrder,
	}

	if err := h.db.Model(&category).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		return
	}

	// 重新加载分类以获取关联数据
	h.db.Where("id = ?", category.ID).Preload("Children").First(&category)

	c.JSON(http.StatusOK, category.ToResponse())
}

// DeleteCategory 删除分类
func (h *CategoryHandler) DeleteCategory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var category models.Category
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch category"})
		}
		return
	}

	// 检查是否有子分类
	var childCount int64
	h.db.Model(&models.Category{}).Where("parent_id = ?", category.ID).Count(&childCount)
	if childCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete category with subcategories"})
		return
	}

	// 检查是否有文档使用此分类
	var docCount int64
	h.db.Model(&models.Document{}).Where("category_id = ?", category.ID).Count(&docCount)
	if docCount > 0 {
		// 将使用此分类的文档的分类设为null
		h.db.Model(&models.Document{}).Where("category_id = ?", category.ID).Update("category_id", nil)
	}

	// 删除分类
	if err := h.db.Delete(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Category deleted successfully"})
}

// wouldCreateCycle 检查是否会创建循环引用
func (h *CategoryHandler) wouldCreateCycle(categoryID, parentID uint) bool {
	// 从parentID开始向上查找，看是否会遇到categoryID
	currentID := parentID
	visited := make(map[uint]bool)

	for currentID != 0 {
		if visited[currentID] {
			// 已经访问过，说明存在循环
			return true
		}
		
		if currentID == categoryID {
			// 找到了目标分类，说明会形成循环
			return true
		}

		visited[currentID] = true

		var category models.Category
		if err := h.db.Where("id = ?", currentID).First(&category).Error; err != nil {
			// 查询失败，停止检查
			break
		}

		if category.ParentID == nil {
			// 到达根分类，没有循环
			break
		}

		currentID = *category.ParentID
	}

	return false
}
