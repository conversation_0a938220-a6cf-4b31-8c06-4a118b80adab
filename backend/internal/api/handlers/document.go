package handlers

import (
	"net/http"
	"strconv"
	"time"

	"jiangjiangNote/backend/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DocumentHandler struct {
	db *gorm.DB
}

func NewDocumentHandler(db *gorm.DB) *DocumentHandler {
	return &DocumentHandler{db: db}
}

// GetDocuments 获取文档列表
func (h *DocumentHandler) GetDocuments(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// 获取查询参数
	categoryID := c.Query("category_id")
	search := c.Query("search")
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("limit", "20"))
	offset := (page - 1) * limit

	// 构建查询
	query := h.db.Where("user_id = ?", userID)

	if categoryID != "" {
		query = query.Where("category_id = ?", categoryID)
	}

	if search != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	var documents []models.Document
	var total int64

	// 获取总数
	query.Model(&models.Document{}).Count(&total)

	// 获取文档列表
	if err := query.Preload("Category").Preload("Tags").
		Order("updated_at DESC").
		Limit(limit).Offset(offset).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch documents"})
		return
	}

	// 转换为列表响应格式
	var response []models.DocumentListResponse
	for _, doc := range documents {
		response = append(response, doc.ToListResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"documents": response,
		"total":     total,
		"page":      page,
		"limit":     limit,
	})
}

// GetDocument 获取文档详情
func (h *DocumentHandler) GetDocument(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid document ID"})
		return
	}

	var document models.Document
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).
		Preload("Category").Preload("Tags").
		First(&document).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch document"})
		}
		return
	}

	// 增加查看次数
	h.db.Model(&document).Update("view_count", document.ViewCount+1)
	document.ViewCount++

	c.JSON(http.StatusOK, document.ToResponse())
}

// CreateDocument 创建文档
func (h *DocumentHandler) CreateDocument(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.DocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证内容类型
	if req.ContentType != "markdown" && req.ContentType != "txt" {
		req.ContentType = "markdown"
	}

	document := models.Document{
		UserID:      userID.(uint),
		Title:       req.Title,
		Content:     req.Content,
		ContentType: req.ContentType,
		CategoryID:  req.CategoryID,
		IsRecent:    true,
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建文档
	if err := tx.Create(&document).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create document"})
		return
	}

	// 处理标签关联
	if len(req.TagIDs) > 0 {
		for _, tagID := range req.TagIDs {
			docTag := models.DocumentTag{
				DocumentID: document.ID,
				TagID:      tagID,
			}
			if err := tx.Create(&docTag).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to associate tags"})
				return
			}
		}
	}

	tx.Commit()

	// 重新加载文档以获取关联数据
	h.db.Where("id = ?", document.ID).
		Preload("Category").Preload("Tags").
		First(&document)

	c.JSON(http.StatusCreated, document.ToResponse())
}

// UpdateDocument 更新文档
func (h *DocumentHandler) UpdateDocument(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid document ID"})
		return
	}

	var req models.DocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证内容类型
	if req.ContentType != "markdown" && req.ContentType != "txt" {
		req.ContentType = "markdown"
	}

	var document models.Document
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&document).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch document"})
		}
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新文档
	updates := map[string]interface{}{
		"title":        req.Title,
		"content":      req.Content,
		"content_type": req.ContentType,
		"category_id":  req.CategoryID,
		"updated_at":   time.Now(),
	}

	if err := tx.Model(&document).Updates(updates).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update document"})
		return
	}

	// 更新标签关联
	if req.TagIDs != nil {
		// 删除现有关联
		if err := tx.Where("document_id = ?", document.ID).Delete(&models.DocumentTag{}).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update tags"})
			return
		}

		// 创建新关联
		for _, tagID := range req.TagIDs {
			docTag := models.DocumentTag{
				DocumentID: document.ID,
				TagID:      tagID,
			}
			if err := tx.Create(&docTag).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to associate tags"})
				return
			}
		}
	}

	tx.Commit()

	// 重新加载文档以获取关联数据
	h.db.Where("id = ?", document.ID).
		Preload("Category").Preload("Tags").
		First(&document)

	c.JSON(http.StatusOK, document.ToResponse())
}

// DeleteDocument 删除文档
func (h *DocumentHandler) DeleteDocument(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid document ID"})
		return
	}

	var document models.Document
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&document).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch document"})
		}
		return
	}

	// 软删除文档
	if err := h.db.Delete(&document).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete document"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Document deleted successfully"})
}

// GetRecentDocuments 获取最近文档
func (h *DocumentHandler) GetRecentDocuments(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var documents []models.Document
	if err := h.db.Where("user_id = ? AND is_recent = ?", userID, true).
		Preload("Category").Preload("Tags").
		Order("updated_at DESC").
		Limit(10).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch recent documents"})
		return
	}

	var response []models.DocumentListResponse
	for _, doc := range documents {
		response = append(response, doc.ToListResponse())
	}

	c.JSON(http.StatusOK, response)
}

// GetFavoriteDocuments 获取收藏文档
func (h *DocumentHandler) GetFavoriteDocuments(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var documents []models.Document
	if err := h.db.Where("user_id = ? AND is_favorite = ?", userID, true).
		Preload("Category").Preload("Tags").
		Order("updated_at DESC").
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch favorite documents"})
		return
	}

	var response []models.DocumentListResponse
	for _, doc := range documents {
		response = append(response, doc.ToListResponse())
	}

	c.JSON(http.StatusOK, response)
}

// ToggleFavorite 切换收藏状态
func (h *DocumentHandler) ToggleFavorite(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid document ID"})
		return
	}

	var document models.Document
	if err := h.db.Where("id = ? AND user_id = ?", id, userID).First(&document).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch document"})
		}
		return
	}

	// 切换收藏状态
	newFavoriteStatus := !document.IsFavorite
	if err := h.db.Model(&document).Update("is_favorite", newFavoriteStatus).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update favorite status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "Favorite status updated",
		"is_favorite": newFavoriteStatus,
	})
}
