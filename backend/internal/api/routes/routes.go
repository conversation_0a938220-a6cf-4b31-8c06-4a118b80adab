package routes

import (
	"jiangjiangNote/backend/internal/api/handlers"
	"jiangjiangNote/backend/internal/api/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(r *gin.Engine, db *gorm.DB) {
	// 添加中间件
	r.Use(middleware.CORSMiddleware())

	// 创建处理器
	authHandler := handlers.NewAuthHandler(db)

	// API路由组
	api := r.Group("/api")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.GET("/profile", middleware.AuthMiddleware(), authHandler.GetProfile)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// 分类管理
			categories := protected.Group("/categories")
			{
				categories.GET("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get categories - TODO"})
				})
				categories.POST("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create category - TODO"})
				})
				categories.PUT("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Update category - TODO"})
				})
				categories.DELETE("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Delete category - TODO"})
				})
			}

			// 文档管理
			documents := protected.Group("/documents")
			{
				documents.GET("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get documents - TODO"})
				})
				documents.GET("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get document - TODO"})
				})
				documents.POST("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create document - TODO"})
				})
				documents.PUT("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Update document - TODO"})
				})
				documents.DELETE("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Delete document - TODO"})
				})
				documents.GET("/recent", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get recent documents - TODO"})
				})
				documents.GET("/favorites", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get favorite documents - TODO"})
				})
			}

			// 标签管理
			tags := protected.Group("/tags")
			{
				tags.GET("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get tags - TODO"})
				})
				tags.POST("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create tag - TODO"})
				})
				tags.PUT("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Update tag - TODO"})
				})
				tags.DELETE("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Delete tag - TODO"})
				})
			}

			// 搜索
			api.GET("/search", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Search - TODO"})
			})
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "CloudNotes API is running",
		})
	})
}
