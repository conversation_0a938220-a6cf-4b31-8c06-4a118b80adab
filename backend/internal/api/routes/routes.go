package routes

import (
	"jiangjiangNote/backend/internal/api/handlers"
	"jiangjiangNote/backend/internal/api/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(r *gin.Engine, db *gorm.DB) {
	// 添加中间件
	r.Use(middleware.CORSMiddleware())

	// 创建处理器
	authHandler := handlers.NewAuthHandler(db)
	documentHandler := handlers.NewDocumentHandler(db)
	categoryHandler := handlers.NewCategoryHandler(db)
	tagHandler := handlers.NewTagHandler(db)
	searchHandler := handlers.NewSearchHandler(db)

	// API路由组
	api := r.Group("/api")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.GET("/profile", middleware.AuthMiddleware(), authHandler.GetProfile)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// 分类管理
			categories := protected.Group("/categories")
			{
				categories.GET("", categoryHandler.GetCategories)
				categories.POST("", categoryHandler.CreateCategory)
				categories.PUT("/:id", categoryHandler.UpdateCategory)
				categories.DELETE("/:id", categoryHandler.DeleteCategory)
			}

			// 文档管理
			documents := protected.Group("/documents")
			{
				documents.GET("", documentHandler.GetDocuments)
				documents.GET("/:id", documentHandler.GetDocument)
				documents.POST("", documentHandler.CreateDocument)
				documents.PUT("/:id", documentHandler.UpdateDocument)
				documents.DELETE("/:id", documentHandler.DeleteDocument)
				documents.GET("/recent", documentHandler.GetRecentDocuments)
				documents.GET("/favorites", documentHandler.GetFavoriteDocuments)
				documents.PUT("/:id/favorite", documentHandler.ToggleFavorite)
			}

			// 标签管理
			tags := protected.Group("/tags")
			{
				tags.GET("", tagHandler.GetTags)
				tags.POST("", tagHandler.CreateTag)
				tags.PUT("/:id", tagHandler.UpdateTag)
				tags.DELETE("/:id", tagHandler.DeleteTag)
				tags.GET("/:id/documents", tagHandler.GetTagDocuments)
			}

			// 搜索
			protected.GET("/search", searchHandler.Search)
			protected.GET("/search/documents", searchHandler.SearchDocuments)
		}
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "JiangJiangNote API is running",
		})
	})
}
