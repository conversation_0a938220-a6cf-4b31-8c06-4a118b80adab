package utils

import (
	"log"
	"os"
	"path/filepath"

	"github.com/joho/godotenv"
)

// LoadEnv 加载环境变量文件
func LoadEnv() error {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		log.Printf("Warning: Could not get current directory: %v", err)
		return err
	}

	// 尝试多个可能的 .env 文件位置
	envPaths := []string{
		".env",                           // 当前目录
		"../.env",                        // 上级目录
		"../../.env",                     // 上上级目录
		filepath.Join(currentDir, ".env"), // 绝对路径
	}

	var loadedPath string
	var loadErr error

	for _, envPath := range envPaths {
		if err := godotenv.Load(envPath); err == nil {
			loadedPath = envPath
			break
		} else {
			loadErr = err
		}
	}

	if loadedPath != "" {
		log.Printf("Successfully loaded environment variables from: %s", loadedPath)
		return nil
	}

	log.Printf("Warning: No .env file found in any of the expected locations")
	log.Printf("Searched paths: %v", envPaths)
	log.Printf("Last error: %v", loadErr)
	log.Println("Using default configuration values")

	return loadErr
}

// PrintEnvStatus 打印环境变量状态
func PrintEnvStatus() {
	log.Println("=== Environment Variables Status ===")
	
	envVars := []string{
		"SERVER_PORT", "GIN_MODE",
		"DB_HOST", "DB_PORT", "DB_USER", "DB_NAME",
		"JWT_SECRET", "JWT_EXPIRE_HOUR",
		"MINIO_ENDPOINT", "MINIO_ACCESS_KEY", "MINIO_BUCKET",
		"REDIS_HOST", "REDIS_PORT",
	}

	for _, envVar := range envVars {
		value := os.Getenv(envVar)
		if value != "" {
			// 隐藏敏感信息
			if envVar == "DB_PASSWORD" || envVar == "JWT_SECRET" || envVar == "MINIO_SECRET_KEY" {
				log.Printf("%s: [HIDDEN]", envVar)
			} else {
				log.Printf("%s: %s", envVar, value)
			}
		} else {
			log.Printf("%s: [NOT SET - using default]", envVar)
		}
	}
	
	log.Println("=====================================")
}
