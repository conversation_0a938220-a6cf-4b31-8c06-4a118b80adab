package models

import (
	"time"

	"gorm.io/gorm"
)

type Tag struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	Name      string         `json:"name" gorm:"not null"`
	Color     string         `json:"color"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User      User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Documents []Document    `json:"documents,omitempty" gorm:"many2many:document_tags;"`
	DocTags   []DocumentTag `json:"-" gorm:"foreignKey:TagID"`
}

type DocumentTag struct {
	DocumentID uint `json:"document_id" gorm:"primaryKey"`
	TagID      uint `json:"tag_id" gorm:"primaryKey"`
	CreatedAt  time.Time
}

type TagRequest struct {
	Name  string `json:"name" binding:"required,max=50"`
	Color string `json:"color"`
}

type TagResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Color     string    `json:"color"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DocCount  int64     `json:"doc_count"`
}

func (t *Tag) ToResponse() TagResponse {
	return TagResponse{
		ID:        t.ID,
		Name:      t.Name,
		Color:     t.Color,
		CreatedAt: t.CreatedAt,
		UpdatedAt: t.UpdatedAt,
	}
}
