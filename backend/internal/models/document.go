package models

import (
	"time"

	"gorm.io/gorm"
)

type Document struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	CategoryID  *uint          `json:"category_id"`
	Title       string         `json:"title" gorm:"not null"`
	Content     string         `json:"content" gorm:"type:text"`
	ContentType string         `json:"content_type" gorm:"default:'markdown'"` // 'markdown' or 'txt'
	FilePath    string         `json:"file_path"`
	IsFavorite  bool           `json:"is_favorite" gorm:"default:false"`
	IsRecent    bool           `json:"is_recent" gorm:"default:false"`
	ViewCount   int            `json:"view_count" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User     User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Category *Category     `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Tags     []Tag         `json:"tags,omitempty" gorm:"many2many:document_tags;"`
	DocTags  []DocumentTag `json:"-" gorm:"foreignKey:DocumentID"`
}

type DocumentRequest struct {
	Title       string `json:"title" binding:"required,max=255"`
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
	CategoryID  *uint  `json:"category_id"`
	TagIDs      []uint `json:"tag_ids"`
}

type DocumentResponse struct {
	ID          uint              `json:"id"`
	Title       string            `json:"title"`
	Content     string            `json:"content"`
	ContentType string            `json:"content_type"`
	CategoryID  *uint             `json:"category_id"`
	Category    *CategoryResponse `json:"category,omitempty"`
	IsFavorite  bool              `json:"is_favorite"`
	IsRecent    bool              `json:"is_recent"`
	ViewCount   int               `json:"view_count"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	Tags        []TagResponse     `json:"tags,omitempty"`
}

type DocumentListResponse struct {
	ID          uint      `json:"id"`
	Title       string    `json:"title"`
	ContentType string    `json:"content_type"`
	CategoryID  *uint     `json:"category_id"`
	IsFavorite  bool      `json:"is_favorite"`
	IsRecent    bool      `json:"is_recent"`
	ViewCount   int       `json:"view_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func (d *Document) ToResponse() DocumentResponse {
	response := DocumentResponse{
		ID:          d.ID,
		Title:       d.Title,
		Content:     d.Content,
		ContentType: d.ContentType,
		CategoryID:  d.CategoryID,
		IsFavorite:  d.IsFavorite,
		IsRecent:    d.IsRecent,
		ViewCount:   d.ViewCount,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
	}

	// 转换分类信息
	if d.Category != nil {
		categoryResp := d.Category.ToResponse()
		response.Category = &categoryResp
	}

	// 转换标签信息
	for _, tag := range d.Tags {
		response.Tags = append(response.Tags, tag.ToResponse())
	}

	return response
}

func (d *Document) ToListResponse() DocumentListResponse {
	return DocumentListResponse{
		ID:          d.ID,
		Title:       d.Title,
		ContentType: d.ContentType,
		CategoryID:  d.CategoryID,
		IsFavorite:  d.IsFavorite,
		IsRecent:    d.IsRecent,
		ViewCount:   d.ViewCount,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
	}
}
