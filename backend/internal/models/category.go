package models

import (
	"time"

	"gorm.io/gorm"
)

type Category struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	Name      string         `json:"name" gorm:"not null"`
	Icon      string         `json:"icon"`
	Color     string         `json:"color"`
	ParentID  *uint          `json:"parent_id"`
	SortOrder int            `json:"sort_order" gorm:"default:0"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User      User       `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Parent    *Category  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children  []Category `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Documents []Document `json:"documents,omitempty" gorm:"foreignKey:CategoryID"`
}

type CategoryRequest struct {
	Name      string `json:"name" binding:"required,max=100"`
	Icon      string `json:"icon"`
	Color     string `json:"color"`
	ParentID  *uint  `json:"parent_id"`
	SortOrder int    `json:"sort_order"`
}

type CategoryResponse struct {
	ID        uint               `json:"id"`
	Name      string             `json:"name"`
	Icon      string             `json:"icon"`
	Color     string             `json:"color"`
	ParentID  *uint              `json:"parent_id"`
	SortOrder int                `json:"sort_order"`
	CreatedAt time.Time          `json:"created_at"`
	UpdatedAt time.Time          `json:"updated_at"`
	Children  []CategoryResponse `json:"children,omitempty"`
	DocCount  int64              `json:"doc_count"`
}

func (c *Category) ToResponse() CategoryResponse {
	response := CategoryResponse{
		ID:        c.ID,
		Name:      c.Name,
		Icon:      c.Icon,
		Color:     c.Color,
		ParentID:  c.ParentID,
		SortOrder: c.SortOrder,
		CreatedAt: c.CreatedAt,
		UpdatedAt: c.UpdatedAt,
	}

	// 转换子分类
	for _, child := range c.Children {
		response.Children = append(response.Children, child.ToResponse())
	}

	return response
}
