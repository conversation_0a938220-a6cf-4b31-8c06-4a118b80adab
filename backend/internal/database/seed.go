package database

import (
	"jiangjiangNote/backend/internal/models"
	"jiangjiangNote/backend/internal/utils"
	"log"

	"gorm.io/gorm"
)

// SeedData 初始化示例数据
func SeedData(db *gorm.DB) error {
	// 检查是否已有用户，如果有则跳过初始化
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	if userCount > 0 {
		log.Println("Database already has users, skipping seed data")
		return nil
	}

	log.Println("Seeding initial data...")

	// 创建示例用户
	hashedPassword, err := utils.HashPassword("123456")
	if err != nil {
		return err
	}

	user := models.User{
		Username:     "demo",
		Email:        "<EMAIL>",
		PasswordHash: hashedPassword,
	}

	if err := db.Create(&user).Error; err != nil {
		return err
	}

	// 创建默认分类
	categories := []models.Category{
		{
			UserID:    user.ID,
			Name:      "工作文档",
			Icon:      "work",
			Color:     "#52c41a",
			SortOrder: 1,
		},
		{
			UserID:    user.ID,
			Name:      "学习笔记",
			Icon:      "study",
			Color:     "#1890ff",
			SortOrder: 2,
		},
		{
			UserID:    user.ID,
			Name:      "生活记录",
			Icon:      "life",
			Color:     "#fa8c16",
			SortOrder: 3,
		},
		{
			UserID:    user.ID,
			Name:      "项目文档",
			Icon:      "project",
			Color:     "#eb2f96",
			SortOrder: 4,
		},
	}

	for _, category := range categories {
		if err := db.Create(&category).Error; err != nil {
			return err
		}
	}

	// 创建默认标签
	tags := []models.Tag{
		{
			UserID: user.ID,
			Name:   "工作",
			Color:  "#108ee9",
		},
		{
			UserID: user.ID,
			Name:   "学习",
			Color:  "#87d068",
		},
		{
			UserID: user.ID,
			Name:   "生活",
			Color:  "#fa8c16",
		},
		{
			UserID: user.ID,
			Name:   "技术",
			Color:  "#722ed1",
		},
		{
			UserID: user.ID,
			Name:   "设计",
			Color:  "#eb2f96",
		},
	}

	for _, tag := range tags {
		if err := db.Create(&tag).Error; err != nil {
			return err
		}
	}

	// 创建示例文档
	documents := []models.Document{
		{
			UserID:      user.ID,
			CategoryID:  &categories[3].ID, // 项目文档
			Title:       "JiangJiangNote 项目开发文档",
			Content:     getProjectDocContent(),
			ContentType: "markdown",
			IsFavorite:  false,
			IsRecent:    true,
			ViewCount:   0,
		},
		{
			UserID:      user.ID,
			CategoryID:  &categories[1].ID, // 学习笔记
			Title:       "React Hooks 学习记录",
			Content:     getReactHooksContent(),
			ContentType: "markdown",
			IsFavorite:  true,
			IsRecent:    true,
			ViewCount:   0,
		},
		{
			UserID:      user.ID,
			CategoryID:  &categories[0].ID, // 工作文档
			Title:       "会议记录模板",
			Content:     getMeetingTemplateContent(),
			ContentType: "txt",
			IsFavorite:  false,
			IsRecent:    false,
			ViewCount:   0,
		},
		{
			UserID:      user.ID,
			CategoryID:  &categories[2].ID, // 生活记录
			Title:       "2024年度计划",
			Content:     getYearPlanContent(),
			ContentType: "markdown",
			IsFavorite:  true,
			IsRecent:    false,
			ViewCount:   0,
		},
	}

	for i, document := range documents {
		if err := db.Create(&document).Error; err != nil {
			return err
		}

		// 为文档添加标签
		var tagIDs []uint
		switch i {
		case 0: // 项目文档
			tagIDs = []uint{tags[0].ID, tags[3].ID} // 工作, 技术
		case 1: // React学习
			tagIDs = []uint{tags[1].ID, tags[3].ID} // 学习, 技术
		case 2: // 会议记录
			tagIDs = []uint{tags[0].ID} // 工作
		case 3: // 年度计划
			tagIDs = []uint{tags[2].ID} // 生活
		}

		for _, tagID := range tagIDs {
			docTag := models.DocumentTag{
				DocumentID: document.ID,
				TagID:      tagID,
			}
			if err := db.Create(&docTag).Error; err != nil {
				return err
			}
		}
	}

	log.Println("Seed data created successfully!")
	log.Println("Demo user credentials:")
	log.Println("  Email: <EMAIL>")
	log.Println("  Password: 123456")

	return nil
}

func getProjectDocContent() string {
	content := "# JiangJiangNote 项目开发文档\n\n"
	content += "## 项目概述\n"
	content += "JiangJiangNote是一款现代化的云笔记系统，类似于语雀，支持Markdown和纯文本编辑。\n\n"
	content += "## 技术栈\n"
	content += "### 后端\n"
	content += "- Go 1.24 + Gin框架\n"
	content += "- GORM (ORM)\n"
	content += "- PostgreSQL 15\n"
	content += "- MinIO (文件存储)\n"
	content += "- JWT (身份认证)\n\n"
	content += "### 前端\n"
	content += "- React 18 + TypeScript\n"
	content += "- Ant Design 5.x (深色主题)\n"
	content += "- React Router v6\n"
	content += "- Zustand (状态管理)\n"
	content += "- Monaco Editor (代码编辑器)\n\n"
	content += "## 功能特性\n"
	content += "1. 用户认证和授权\n"
	content += "2. 文档的增删改查\n"
	content += "3. 分类管理\n"
	content += "4. 标签系统\n"
	content += "5. 搜索功能\n"
	content += "6. 收藏功能\n\n"
	content += "## 开发进度\n"
	content += "- [x] 用户认证系统\n"
	content += "- [x] 基础布局和UI\n"
	content += "- [x] 文档编辑器\n"
	content += "- [x] 分类和标签管理\n"
	content += "- [x] 搜索功能\n"
	content += "- [ ] 文件上传功能\n"
	content += "- [ ] 移动端适配\n"
	content += "- [ ] 实时协作功能\n\n"
	content += "## 注意事项\n"
	content += "- 请确保在开发过程中遵循代码规范\n"
	content += "- 提交代码前运行测试\n"
	content += "- 保持API文档更新"
	return content
}

func getReactHooksContent() string {
	content := "# React Hooks 学习记录\n\n"
	content += "## useState\n"
	content += "用于在函数组件中添加状态。\n\n"
	content += "```javascript\n"
	content += "const [count, setCount] = useState(0);\n"
	content += "```\n\n"
	content += "## useEffect\n"
	content += "用于处理副作用，如数据获取、订阅等。\n\n"
	content += "```javascript\n"
	content += "useEffect(() => {\n"
	content += "  // 副作用逻辑\n"
	content += "  return () => {\n"
	content += "    // 清理逻辑\n"
	content += "  };\n"
	content += "}, [dependencies]);\n"
	content += "```\n\n"
	content += "## useContext\n"
	content += "用于在组件树中传递数据，避免prop drilling。\n\n"
	content += "```javascript\n"
	content += "const value = useContext(MyContext);\n"
	content += "```\n\n"
	content += "## 最佳实践\n"
	content += "1. 使用ESLint插件检查Hooks规则\n"
	content += "2. 将相关的状态逻辑组合在一起\n"
	content += "3. 使用useCallback和useMemo优化性能\n"
	content += "4. 自定义Hooks要以\"use\"开头"
	return content
}

func getMeetingTemplateContent() string {
	content := "会议记录模板\n\n"
	content += "会议主题：\n"
	content += "会议时间：\n"
	content += "参会人员：\n"
	content += "会议地点：\n\n"
	content += "议题：\n"
	content += "1. \n"
	content += "2. \n"
	content += "3. \n\n"
	content += "讨论内容：\n\n\n"
	content += "决议事项：\n"
	content += "1. \n"
	content += "2. \n"
	content += "3. \n\n"
	content += "行动计划：\n"
	content += "- 负责人：\n"
	content += "- 截止时间：\n"
	content += "- 具体任务：\n\n"
	content += "下次会议：\n"
	content += "时间：\n"
	content += "议题：\n\n"
	content += "备注："
	return content
}

func getYearPlanContent() string {
	content := "# 2024年度计划\n\n"
	content += "## 工作目标\n"
	content += "- [ ] 完成JiangJiangNote项目开发\n"
	content += "- [ ] 学习新技术栈\n"
	content += "- [ ] 提升代码质量和开发效率\n\n"
	content += "## 学习计划\n"
	content += "### 技术学习\n"
	content += "- [ ] 深入学习Go语言高级特性\n"
	content += "- [ ] 掌握微服务架构\n"
	content += "- [ ] 学习云原生技术\n\n"
	content += "### 软技能\n"
	content += "- [ ] 提升沟通能力\n"
	content += "- [ ] 学习项目管理\n"
	content += "- [ ] 培养团队协作精神\n\n"
	content += "## 生活目标\n"
	content += "- [ ] 保持健康的作息时间\n"
	content += "- [ ] 定期运动锻炼\n"
	content += "- [ ] 培养新的兴趣爱好\n"
	content += "- [ ] 多陪伴家人朋友\n\n"
	content += "## 财务规划\n"
	content += "- [ ] 制定预算计划\n"
	content += "- [ ] 学习投资理财\n"
	content += "- [ ] 建立应急基金\n\n"
	content += "## 季度回顾\n"
	content += "### Q1 (1-3月)\n"
	content += "- 目标：\n"
	content += "- 完成情况：\n\n"
	content += "### Q2 (4-6月)\n"
	content += "- 目标：\n"
	content += "- 完成情况：\n\n"
	content += "### Q3 (7-9月)\n"
	content += "- 目标：\n"
	content += "- 完成情况：\n\n"
	content += "### Q4 (10-12月)\n"
	content += "- 目标：\n"
	content += "- 完成情况："
	return content
}
