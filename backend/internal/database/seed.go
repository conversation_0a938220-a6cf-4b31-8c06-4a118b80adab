package database

import (
	"jiangjiangNote/backend/internal/models"
	"jiangjiangNote/backend/internal/utils"
	"log"

	"gorm.io/gorm"
)

// SeedData 初始化示例数据
func SeedData(db *gorm.DB) error {
	// 检查是否已有用户，如果有则跳过初始化
	var userCount int64
	db.Model(&models.User{}).Count(&userCount)
	if userCount > 0 {
		log.Println("Database already has users, skipping seed data")
		return nil
	}

	log.Println("Seeding initial data...")

	// 创建示例用户
	hashedPassword, err := utils.HashPassword("123456")
	if err != nil {
		return err
	}

	user := models.User{
		Username:     "demo",
		Email:        "<EMAIL>",
		PasswordHash: hashedPassword,
	}

	if err := db.Create(&user).Error; err != nil {
		return err
	}

	// 创建默认分类
	categories := []models.Category{
		{
			UserID:    user.ID,
			Name:      "工作文档",
			Icon:      "work",
			Color:     "#52c41a",
			SortOrder: 1,
		},
		{
			UserID:    user.ID,
			Name:      "学习笔记",
			Icon:      "study",
			Color:     "#1890ff",
			SortOrder: 2,
		},
		{
			UserID:    user.ID,
			Name:      "生活记录",
			Icon:      "life",
			Color:     "#fa8c16",
			SortOrder: 3,
		},
		{
			UserID:    user.ID,
			Name:      "项目文档",
			Icon:      "project",
			Color:     "#eb2f96",
			SortOrder: 4,
		},
	}

	for _, category := range categories {
		if err := db.Create(&category).Error; err != nil {
			return err
		}
	}

	// 创建默认标签
	tags := []models.Tag{
		{
			UserID: user.ID,
			Name:   "工作",
			Color:  "#108ee9",
		},
		{
			UserID: user.ID,
			Name:   "学习",
			Color:  "#87d068",
		},
		{
			UserID: user.ID,
			Name:   "生活",
			Color:  "#fa8c16",
		},
		{
			UserID: user.ID,
			Name:   "技术",
			Color:  "#722ed1",
		},
		{
			UserID: user.ID,
			Name:   "设计",
			Color:  "#eb2f96",
		},
	}

	for _, tag := range tags {
		if err := db.Create(&tag).Error; err != nil {
			return err
		}
	}

	// 创建示例文档
	documents := []models.Document{
		{
			UserID:      user.ID,
			CategoryID:  &categories[3].ID, // 项目文档
			Title:       "JiangJiangNote 项目开发文档",
			Content:     getProjectDocContent(),
			ContentType: "markdown",
			IsFavorite:  false,
			IsRecent:    true,
			ViewCount:   0,
		},
		{
			UserID:      user.ID,
			CategoryID:  &categories[1].ID, // 学习笔记
			Title:       "React Hooks 学习记录",
			Content:     getReactHooksContent(),
			ContentType: "markdown",
			IsFavorite:  true,
			IsRecent:    true,
			ViewCount:   0,
		},
		{
			UserID:      user.ID,
			CategoryID:  &categories[0].ID, // 工作文档
			Title:       "会议记录模板",
			Content:     getMeetingTemplateContent(),
			ContentType: "txt",
			IsFavorite:  false,
			IsRecent:    false,
			ViewCount:   0,
		},
		{
			UserID:      user.ID,
			CategoryID:  &categories[2].ID, // 生活记录
			Title:       "2024年度计划",
			Content:     getYearPlanContent(),
			ContentType: "markdown",
			IsFavorite:  true,
			IsRecent:    false,
			ViewCount:   0,
		},
	}

	for i, document := range documents {
		if err := db.Create(&document).Error; err != nil {
			return err
		}

		// 为文档添加标签
		var tagIDs []uint
		switch i {
		case 0: // 项目文档
			tagIDs = []uint{tags[0].ID, tags[3].ID} // 工作, 技术
		case 1: // React学习
			tagIDs = []uint{tags[1].ID, tags[3].ID} // 学习, 技术
		case 2: // 会议记录
			tagIDs = []uint{tags[0].ID} // 工作
		case 3: // 年度计划
			tagIDs = []uint{tags[2].ID} // 生活
		}

		for _, tagID := range tagIDs {
			docTag := models.DocumentTag{
				DocumentID: document.ID,
				TagID:      tagID,
			}
			if err := db.Create(&docTag).Error; err != nil {
				return err
			}
		}
	}

	log.Println("Seed data created successfully!")
	log.Println("Demo user credentials:")
	log.Println("  Email: <EMAIL>")
	log.Println("  Password: 123456")

	return nil
}

func getProjectDocContent() string {
	return `# JiangJiangNote 项目开发文档

## 项目概述
JiangJiangNote是一款现代化的云笔记系统，类似于语雀，支持Markdown和纯文本编辑。

## 技术栈
### 后端
- Go 1.24 + Gin框架
- GORM (ORM)
- PostgreSQL 15
- MinIO (文件存储)
- JWT (身份认证)

### 前端
- React 18 + TypeScript
- Ant Design 5.x (深色主题)
- React Router v6
- Zustand (状态管理)
- Monaco Editor (代码编辑器)

## 功能特性
1. ✅ 用户认证和授权
2. ✅ 文档的增删改查
3. ✅ 分类管理
4. ✅ 标签系统
5. ✅ 搜索功能
6. ✅ 收藏功能
7. 🔄 文件上传 (开发中)
8. 🔄 实时协作 (计划中)

## 开发进度
- [x] 用户认证系统
- [x] 基础布局和UI
- [x] 文档编辑器
- [x] 分类和标签管理
- [x] 搜索功能
- [ ] 文件上传功能
- [ ] 移动端适配
- [ ] 实时协作功能

## 注意事项
- 请确保在开发过程中遵循代码规范
- 提交代码前运行测试
- 保持API文档更新`
}

func getReactHooksContent() string {
	return `# React Hooks 学习记录

## useState
用于在函数组件中添加状态。

\`\`\`javascript
const [count, setCount] = useState(0);
\`\`\`

## useEffect
用于处理副作用，如数据获取、订阅等。

\`\`\`javascript
useEffect(() => {
  // 副作用逻辑
  return () => {
    // 清理逻辑
  };
}, [dependencies]);
\`\`\`

## useContext
用于在组件树中传递数据，避免prop drilling。

\`\`\`javascript
const value = useContext(MyContext);
\`\`\`

## 自定义Hooks
可以将组件逻辑提取到可重用的函数中。

\`\`\`javascript
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = () => setCount(count + 1);
  const decrement = () => setCount(count - 1);
  
  return { count, increment, decrement };
}
\`\`\`

## 最佳实践
1. 使用ESLint插件检查Hooks规则
2. 将相关的状态逻辑组合在一起
3. 使用useCallback和useMemo优化性能
4. 自定义Hooks要以"use"开头`
}

func getMeetingTemplateContent() string {
	return `会议记录模板

会议主题：
会议时间：
参会人员：
会议地点：

议题：
1. 
2. 
3. 

讨论内容：


决议事项：
1. 
2. 
3. 

行动计划：
- 负责人：
- 截止时间：
- 具体任务：

下次会议：
时间：
议题：

备注：`
}

func getYearPlanContent() string {
	return `# 2024年度计划

## 工作目标
- [ ] 完成JiangJiangNote项目开发
- [ ] 学习新技术栈
- [ ] 提升代码质量和开发效率

## 学习计划
### 技术学习
- [ ] 深入学习Go语言高级特性
- [ ] 掌握微服务架构
- [ ] 学习云原生技术

### 软技能
- [ ] 提升沟通能力
- [ ] 学习项目管理
- [ ] 培养团队协作精神

## 生活目标
- [ ] 保持健康的作息时间
- [ ] 定期运动锻炼
- [ ] 培养新的兴趣爱好
- [ ] 多陪伴家人朋友

## 财务规划
- [ ] 制定预算计划
- [ ] 学习投资理财
- [ ] 建立应急基金

## 季度回顾
### Q1 (1-3月)
- 目标：
- 完成情况：

### Q2 (4-6月)
- 目标：
- 完成情况：

### Q3 (7-9月)
- 目标：
- 完成情况：

### Q4 (10-12月)
- 目标：
- 完成情况：`
}`
