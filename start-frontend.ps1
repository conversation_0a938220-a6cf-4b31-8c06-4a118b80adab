# JiangJiangNote 前端启动脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    JiangJiangNote 前端启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Node.js是否安装
try {
    $nodeVersion = node --version 2>$null
    Write-Host "[✓] Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[✗] 未检测到Node.js环境，请先安装Node.js 18+" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查npm是否可用
try {
    $npmVersion = npm --version 2>$null
    Write-Host "[✓] npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "[✗] npm不可用" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 进入前端目录
if (Test-Path "frontend") {
    Set-Location "frontend"
    Write-Host "[✓] 进入前端目录" -ForegroundColor Green
} else {
    Write-Host "[✗] frontend目录不存在" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查package.json
if (Test-Path "package.json") {
    Write-Host "[✓] 找到package.json" -ForegroundColor Green
} else {
    Write-Host "[✗] package.json不存在" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 清理可能的缓存和依赖问题
Write-Host "[信息] 清理缓存..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
    Write-Host "[✓] 已清理node_modules" -ForegroundColor Green
}

if (Test-Path "package-lock.json") {
    Remove-Item "package-lock.json" -ErrorAction SilentlyContinue
    Write-Host "[✓] 已清理package-lock.json" -ForegroundColor Green
}

# 安装依赖
Write-Host "[信息] 安装依赖..." -ForegroundColor Yellow
try {
    npm install
    Write-Host "[✓] 依赖安装成功" -ForegroundColor Green
} catch {
    Write-Host "[✗] 依赖安装失败" -ForegroundColor Red
    Write-Host "尝试使用yarn或者检查网络连接" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

# 检查关键依赖
Write-Host "[信息] 检查关键依赖..." -ForegroundColor Yellow
$keyDeps = @("react", "react-dom", "antd", "typescript", "react-scripts")
foreach ($dep in $keyDeps) {
    if (Test-Path "node_modules/$dep") {
        Write-Host "[✓] $dep 已安装" -ForegroundColor Green
    } else {
        Write-Host "[!] $dep 可能未正确安装" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "[信息] 启动开发服务器..." -ForegroundColor Cyan
Write-Host "如果遇到ESLint错误，可以忽略，应用仍然可以正常运行" -ForegroundColor Yellow
Write-Host ""

# 启动开发服务器
try {
    npm start
} catch {
    Write-Host "[✗] 启动失败" -ForegroundColor Red
    Write-Host "请检查错误信息并手动解决" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
}

# 返回上级目录
Set-Location ".."
