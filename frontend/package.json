{"name": "cloudnotes-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "@monaco-editor/react": "^4.6.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "antd": "^5.12.8", "axios": "^1.6.2", "marked": "^11.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/marked": "^6.0.0"}, "proxy": "http://localhost:8080"}