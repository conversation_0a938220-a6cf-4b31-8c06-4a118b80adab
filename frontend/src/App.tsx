/**
 * JiangJiangNote 前端应用主组件
 *
 * 这是一个现代化的云笔记系统前端应用，支持：
 * - 用户认证（登录/注册）
 * - Markdown和纯文本文档编辑
 * - 文档分类和标签管理
 * - 全文搜索功能
 * - 响应式设计
 */

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from './store/authStore';
import Layout from './components/Layout';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import HomePage from './pages/HomePage';
import DocumentPage from './pages/DocumentPage';
import SearchPage from './pages/SearchPage';
import './App.css';

/**
 * App 主应用组件
 *
 * 功能：
 * 1. 配置Ant Design主题（深色主题 + 紫色主色调）
 * 2. 设置路由结构和认证保护
 * 3. 管理用户认证状态
 * 4. 提供中文本地化支持
 */
const App: React.FC = () => {
  const { isAuthenticated, loadUserFromStorage } = useAuthStore();

  // 应用启动时从本地存储加载用户信息
  useEffect(() => {
    loadUserFromStorage();
  }, [loadUserFromStorage]);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#8b5cf6',
          colorBgContainer: '#1a1a2e',
          colorBgElevated: '#16213e',
          colorBgLayout: '#0f0f23',
          colorText: '#ffffff',
          colorTextSecondary: '#a0a0a0',
          colorBorder: '#2a2a4a',
        },
      }}
    >
      <Router>
        <div className="App">
          <Routes>
            <Route 
              path="/login" 
              element={!isAuthenticated ? <LoginPage /> : <Navigate to="/" />} 
            />
            <Route 
              path="/register" 
              element={!isAuthenticated ? <RegisterPage /> : <Navigate to="/" />} 
            />
            <Route
              path="/*"
              element={
                isAuthenticated ? (
                  <Layout>
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/document/:id" element={<DocumentPage />} />
                      <Route path="/category/:id" element={<HomePage />} />
                      <Route path="/recent" element={<HomePage />} />
                      <Route path="/favorites" element={<HomePage />} />
                      <Route path="/search" element={<SearchPage />} />
                      <Route path="*" element={<Navigate to="/" />} />
                    </Routes>
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
};

export default App;
