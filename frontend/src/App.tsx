import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useAuthStore } from './store/authStore';
import Layout from './components/Layout';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import HomePage from './pages/HomePage';
import DocumentPage from './pages/DocumentPage';
import SearchPage from './pages/SearchPage';
import './App.css';

const App: React.FC = () => {
  const { isAuthenticated, loadUserFromStorage } = useAuthStore();

  useEffect(() => {
    loadUserFromStorage();
  }, [loadUserFromStorage]);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorPrimary: '#8b5cf6',
          colorBgContainer: '#1a1a2e',
          colorBgElevated: '#16213e',
          colorBgLayout: '#0f0f23',
          colorText: '#ffffff',
          colorTextSecondary: '#a0a0a0',
          colorBorder: '#2a2a4a',
        },
      }}
    >
      <Router>
        <div className="App">
          <Routes>
            <Route 
              path="/login" 
              element={!isAuthenticated ? <LoginPage /> : <Navigate to="/" />} 
            />
            <Route 
              path="/register" 
              element={!isAuthenticated ? <RegisterPage /> : <Navigate to="/" />} 
            />
            <Route
              path="/*"
              element={
                isAuthenticated ? (
                  <Layout>
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/document/:id" element={<DocumentPage />} />
                      <Route path="/category/:id" element={<HomePage />} />
                      <Route path="/recent" element={<HomePage />} />
                      <Route path="/favorites" element={<HomePage />} />
                      <Route path="/search" element={<SearchPage />} />
                      <Route path="*" element={<Navigate to="/" />} />
                    </Routes>
                  </Layout>
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
};

export default App;
