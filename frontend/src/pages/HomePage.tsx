import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Tag, Button, Dropdown, Space, Empty } from 'antd';
import {
  FileTextOutlined,
  FileMarkdownOutlined,
  MoreOutlined,
  StarOutlined,
  StarFilled,
  EyeOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { DocumentListItem } from '@/types';
import type { MenuProps } from 'antd';

const { Meta } = Card;

// 模拟数据
const mockDocuments: DocumentListItem[] = [
  {
    id: 1,
    title: '项目开发文档',
    content_type: 'markdown',
    category_id: 1,
    is_favorite: false,
    is_recent: true,
    view_count: 25,
    created_at: '2024-01-20T10:30:00Z',
    updated_at: '2024-01-20T15:45:00Z',
  },
  {
    id: 2,
    title: '学习记录 - React Hooks',
    content_type: 'markdown',
    category_id: 2,
    is_favorite: true,
    is_recent: true,
    view_count: 18,
    created_at: '2024-01-19T14:20:00Z',
    updated_at: '2024-01-19T16:30:00Z',
  },
  {
    id: 3,
    title: '会议记录',
    content_type: 'txt',
    category_id: 1,
    is_favorite: false,
    is_recent: false,
    view_count: 8,
    created_at: '2024-01-18T09:15:00Z',
    updated_at: '2024-01-18T11:20:00Z',
  },
  {
    id: 4,
    title: '旅行计划',
    content_type: 'markdown',
    category_id: 3,
    is_favorite: true,
    is_recent: false,
    view_count: 12,
    created_at: '2024-01-17T16:45:00Z',
    updated_at: '2024-01-17T18:30:00Z',
  },
];

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [documents, setDocuments] = useState<DocumentListItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDocuments();
  }, [location.pathname]);

  const loadDocuments = async () => {
    setLoading(true);
    try {
      // TODO: 根据路径加载不同的文档
      const path = location.pathname;
      let filteredDocs = mockDocuments;

      if (path === '/recent') {
        filteredDocs = mockDocuments.filter(doc => doc.is_recent);
      } else if (path === '/favorites') {
        filteredDocs = mockDocuments.filter(doc => doc.is_favorite);
      } else if (path.startsWith('/category/')) {
        const categoryId = parseInt(path.split('/')[2]);
        filteredDocs = mockDocuments.filter(doc => doc.category_id === categoryId);
      }

      setDocuments(filteredDocs);
    } catch (error) {
      console.error('加载文档失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentClick = (doc: DocumentListItem) => {
    navigate(`/document/${doc.id}`);
  };

  const handleToggleFavorite = (doc: DocumentListItem, e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: 实现收藏/取消收藏功能
    setDocuments(prev =>
      prev.map(d =>
        d.id === doc.id ? { ...d, is_favorite: !d.is_favorite } : d
      )
    );
  };

  const getDocumentMenuItems = (doc: DocumentListItem): MenuProps['items'] => [
    {
      key: 'edit',
      label: '编辑',
      onClick: () => navigate(`/document/${doc.id}`),
    },
    {
      key: 'favorite',
      label: doc.is_favorite ? '取消收藏' : '收藏',
      onClick: (e) => handleToggleFavorite(doc, e as any),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
      onClick: () => {
        // TODO: 实现删除功能
        console.log('删除文档:', doc.id);
      },
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/recent') return '最近';
    if (path === '/favorites') return '收藏';
    if (path.startsWith('/category/')) return '分类文档';
    return '全部笔记';
  };

  return (
    <div className="document-grid">
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 style={{ color: '#ffffff', margin: 0, fontSize: '24px' }}>
          {getPageTitle()}
        </h2>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button icon={<FileTextOutlined />}>列表视图</Button>
          <Button type="primary" icon={<FileTextOutlined />}>卡片视图</Button>
        </div>
      </div>

      {documents.length === 0 ? (
        <Empty
          description="暂无文档"
          style={{ marginTop: '100px' }}
        />
      ) : (
        <Row gutter={[16, 16]}>
          {documents.map((doc) => (
            <Col xs={24} sm={12} md={8} lg={6} key={doc.id}>
              <Card
                className="document-card"
                hoverable
                onClick={() => handleDocumentClick(doc)}
                actions={[
                  <Button
                    type="text"
                    icon={doc.is_favorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                    onClick={(e) => handleToggleFavorite(doc, e)}
                  />,
                  <Space>
                    <EyeOutlined />
                    {doc.view_count}
                  </Space>,
                  <Dropdown
                    menu={{ items: getDocumentMenuItems(doc) }}
                    trigger={['click']}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Button type="text" icon={<MoreOutlined />} />
                  </Dropdown>,
                ]}
              >
                <Meta
                  avatar={
                    doc.content_type === 'markdown' ? (
                      <FileMarkdownOutlined style={{ fontSize: '24px', color: '#8b5cf6' }} />
                    ) : (
                      <FileTextOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                    )
                  }
                  title={doc.title}
                  description={
                    <div>
                      <div style={{ marginBottom: '8px' }}>
                        <Tag color={doc.content_type === 'markdown' ? 'purple' : 'green'}>
                          {doc.content_type === 'markdown' ? 'Markdown' : '纯文本'}
                        </Tag>
                      </div>
                      <div className="document-meta">
                        <span>
                          <CalendarOutlined style={{ marginRight: '4px' }} />
                          {formatDate(doc.updated_at)}
                        </span>
                      </div>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default HomePage;
