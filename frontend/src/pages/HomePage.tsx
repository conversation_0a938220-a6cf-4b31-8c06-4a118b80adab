import React, { useEffect } from 'react';
import { Row, Col, Card, Tag, Button, Dropdown, Space, Empty, message } from 'antd';
import {
  FileTextOutlined,
  FileMarkdownOutlined,
  MoreOutlined,
  StarOutlined,
  StarFilled,
  EyeOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { DocumentListItem } from '../types';
import { useDocumentStore } from '../store/documentStore';
import type { MenuProps } from 'antd';

const { Meta } = Card;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    documents,
    loading,
    error,
    fetchDocuments,
    fetchRecentDocuments,
    fetchFavoriteDocuments,
    toggleFavorite,
    deleteDocument,
    clearError
  } = useDocumentStore();

  useEffect(() => {
    loadDocuments();
  }, [location.pathname]);

  const loadDocuments = async () => {
    try {
      const path = location.pathname;

      if (path === '/recent') {
        await fetchRecentDocuments();
      } else if (path === '/favorites') {
        await fetchFavoriteDocuments();
      } else if (path.startsWith('/category/')) {
        const categoryId = parseInt(path.split('/')[2]);
        await fetchDocuments({ category_id: categoryId });
      } else {
        await fetchDocuments();
      }
    } catch (error) {
      console.error('加载文档失败:', error);
    }
  };

  const handleDocumentClick = (doc: DocumentListItem) => {
    navigate(`/document/${doc.id}`);
  };

  const handleToggleFavorite = async (doc: DocumentListItem, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleFavorite(doc.id);
      message.success(doc.is_favorite ? '已取消收藏' : '已收藏');
    } catch (error) {
      message.error('操作失败');
    }
  };

  const getDocumentMenuItems = (doc: DocumentListItem): MenuProps['items'] => [
    {
      key: 'edit',
      label: '编辑',
      onClick: () => navigate(`/document/${doc.id}`),
    },
    {
      key: 'favorite',
      label: doc.is_favorite ? '取消收藏' : '收藏',
      onClick: (e) => handleToggleFavorite(doc, e as any),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
      onClick: async () => {
        try {
          await deleteDocument(doc.id);
          message.success('文档已删除');
        } catch (error) {
          message.error('删除失败');
        }
      },
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/recent') return '最近';
    if (path === '/favorites') return '收藏';
    if (path.startsWith('/category/')) return '分类文档';
    return '全部笔记';
  };

  return (
    <div className="document-grid">
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 style={{ color: '#ffffff', margin: 0, fontSize: '24px' }}>
          {getPageTitle()}
        </h2>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button icon={<FileTextOutlined />}>列表视图</Button>
          <Button type="primary" icon={<FileTextOutlined />}>卡片视图</Button>
        </div>
      </div>

      {documents.length === 0 ? (
        <Empty
          description="暂无文档"
          style={{ marginTop: '100px' }}
        />
      ) : (
        <Row gutter={[16, 16]}>
          {documents.map((doc) => (
            <Col xs={24} sm={12} md={8} lg={6} key={doc.id}>
              <Card
                className="document-card"
                hoverable
                onClick={() => handleDocumentClick(doc)}
                actions={[
                  <Button
                    type="text"
                    icon={doc.is_favorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                    onClick={(e) => handleToggleFavorite(doc, e)}
                  />,
                  <Space>
                    <EyeOutlined />
                    {doc.view_count}
                  </Space>,
                  <Dropdown
                    menu={{ items: getDocumentMenuItems(doc) }}
                    trigger={['click']}
                  >
                    <Button
                      type="text"
                      icon={<MoreOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Dropdown>,
                ]}
              >
                <Meta
                  avatar={
                    doc.content_type === 'markdown' ? (
                      <FileMarkdownOutlined style={{ fontSize: '24px', color: '#8b5cf6' }} />
                    ) : (
                      <FileTextOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                    )
                  }
                  title={doc.title}
                  description={
                    <div>
                      <div style={{ marginBottom: '8px' }}>
                        <Tag color={doc.content_type === 'markdown' ? 'purple' : 'green'}>
                          {doc.content_type === 'markdown' ? 'Markdown' : '纯文本'}
                        </Tag>
                      </div>
                      <div className="document-meta">
                        <span>
                          <CalendarOutlined style={{ marginRight: '4px' }} />
                          {formatDate(doc.updated_at)}
                        </span>
                      </div>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default HomePage;
