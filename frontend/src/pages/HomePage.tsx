/**
 * HomePage 主页组件
 *
 * 这是JiangJiangNote的主页面，采用双栏布局设计：
 * - 左侧：文档网格展示区域，显示用户的所有文档
 * - 右侧：AI助手面板，提供智能功能和快捷工具
 *
 * 功能特性：
 * - 响应式文档卡片网格
 * - 文档类型识别（Markdown/纯文本）
 * - 文档查看统计和时间显示
 * - AI助手集成面板
 * - 快捷工具访问
 */

import React, { useEffect, useState } from 'react';
import { Row, Col, Button, Space, Empty } from 'antd';
import {
  FileTextOutlined,
  FileMarkdownOutlined,
  EyeOutlined,
  CalendarOutlined,
  RobotOutlined,
  SettingOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  BulbOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { DocumentListItem } from '../types';
import { useDocumentStore } from '../store/documentStore';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { documents, loading, fetchDocuments } = useDocumentStore();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 组件挂载时获取文档列表
  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  /**
   * 处理文档卡片点击事件
   * 导航到文档详情页面
   */
  const handleDocumentClick = (doc: DocumentListItem) => {
    navigate(`/document/${doc.id}`);
  };

  /**
   * 格式化日期显示
   * 将日期转换为相对时间格式（今天、昨天、X天前）
   */
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  /**
   * 根据当前路由获取页面标题
   */
  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/recent') return '最近';
    if (path === '/favorites') return '收藏';
    if (path.startsWith('/category/')) return '分类文档';
    return '全部笔记';
  };

  /**
   * AI助手面板组件
   *
   * 右侧固定面板，包含：
   * - AI助手介绍和启动按钮
   * - 快捷工具网格（导入、分享、优化、回收站）
   * - 支持格式说明（Markdown、纯文本）
   * - 帮助中心链接
   */
  const AIAssistantPanel = () => (
    <div style={{
      width: '320px',
      background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      padding: '24px',
      borderRadius: '16px',
      margin: '24px',
      color: 'white',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: '-50px',
        right: '-50px',
        width: '100px',
        height: '100px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '50%',
      }} />
      
      <div style={{ position: 'relative', zIndex: 1 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <RobotOutlined style={{ fontSize: '24px', marginRight: '8px' }} />
          <span style={{ fontSize: '18px', fontWeight: 600 }}>AI助手</span>
        </div>
        
        <p style={{ 
          fontSize: '14px', 
          lineHeight: '1.5', 
          marginBottom: '20px',
          opacity: 0.9 
        }}>
          AI帮助您整理和优化笔记内容
        </p>
        
        <Button
          type="primary"
          size="large"
          onClick={() => {
            // TODO: 实现AI助手功能
            console.log('启动AI助手');
          }}
          style={{
            width: '100%',
            background: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '8px',
            marginBottom: '24px',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
          }}
        >
          开始使用
        </Button>
        
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{ color: 'white', marginBottom: '12px', fontSize: '16px' }}>快捷工具</h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
            <div
              style={{
                textAlign: 'center',
                padding: '12px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onClick={() => {
                // TODO: 实现导入功能
                console.log('导入文档');
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >
              <ImportOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>导入</div>
            </div>
            <div
              style={{
                textAlign: 'center',
                padding: '12px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onClick={() => {
                // TODO: 实现分享功能
                console.log('分享文档');
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >
              <ExportOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>分享</div>
            </div>
            <div
              style={{
                textAlign: 'center',
                padding: '12px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onClick={() => {
                // TODO: 实现优化功能
                console.log('优化文档');
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >
              <SettingOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>优化</div>
            </div>
            <div
              style={{
                textAlign: 'center',
                padding: '12px',
                borderRadius: '8px',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onClick={() => {
                navigate('/trash');
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
            >
              <DeleteOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>回收站</div>
            </div>
          </div>
        </div>
        
        <div>
          <h4 style={{ color: 'white', marginBottom: '12px', fontSize: '16px' }}>支持格式</h4>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              background: 'rgba(255, 255, 255, 0.15)', 
              padding: '8px 12px', 
              borderRadius: '6px',
              marginBottom: '8px',
              fontSize: '14px'
            }}>
              <FileMarkdownOutlined style={{ marginRight: '8px' }} />
              Markdown
              <div style={{ fontSize: '12px', opacity: 0.8 }}>支持标准 Markdown 语法</div>
            </div>
            <div style={{ 
              background: 'rgba(255, 255, 255, 0.15)', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px'
            }}>
              <FileTextOutlined style={{ marginRight: '8px' }} />
              纯文本
              <div style={{ fontSize: '12px', opacity: 0.8 }}>简单易用的文本格式</div>
            </div>
          </div>
        </div>
        
        <div style={{ 
          background: 'rgba(255, 255, 255, 0.1)', 
          padding: '12px', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <BulbOutlined style={{ fontSize: '20px', marginBottom: '8px' }} />
          <div style={{ fontSize: '14px', fontWeight: 500 }}>帮助中心</div>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>遇到问题？查看使用指南和常见问题</div>
          <Button
            size="small"
            onClick={() => {
              // TODO: 打开帮助文档
              window.open('/help', '_blank');
            }}
            style={{
              marginTop: '8px',
              background: 'transparent',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              color: 'white',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
            }}
          >
            查看帮助
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div style={{ display: 'flex', height: '100%', background: '#1a1a2e' }}>
      {/* 主内容区域 */}
      <div style={{ flex: 1, padding: '24px' }}>
        {/* 页面标题和工具栏 */}
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ color: '#ffffff', margin: 0, fontSize: '24px', fontWeight: 600 }}>
            {getPageTitle()}
          </h2>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              icon={<EyeOutlined />}
              type={viewMode === 'grid' ? 'primary' : 'default'}
              onClick={() => setViewMode('grid')}
              style={{
                background: viewMode === 'grid'
                  ? 'linear-gradient(135deg, #8b5cf6, #a855f7)'
                  : 'rgba(255, 255, 255, 0.1)',
                border: viewMode === 'grid'
                  ? 'none'
                  : '1px solid rgba(255, 255, 255, 0.2)',
                color: viewMode === 'grid' ? '#ffffff' : 'rgba(255, 255, 255, 0.8)',
                borderRadius: '8px',
                fontWeight: '500'
              }}
            >
              网格视图
            </Button>
            <Button
              icon={<FileTextOutlined />}
              type={viewMode === 'list' ? 'primary' : 'default'}
              onClick={() => setViewMode('list')}
              style={{
                background: viewMode === 'list'
                  ? 'linear-gradient(135deg, #8b5cf6, #a855f7)'
                  : 'rgba(255, 255, 255, 0.1)',
                border: viewMode === 'list'
                  ? 'none'
                  : '1px solid rgba(255, 255, 255, 0.2)',
                color: viewMode === 'list' ? '#ffffff' : 'rgba(255, 255, 255, 0.8)',
                borderRadius: '8px',
                fontWeight: '500'
              }}
            >
              列表视图
            </Button>
          </div>
        </div>

        {/* 文档网格 */}
        {documents.length === 0 ? (
          <Empty
            description="暂无文档"
            style={{ marginTop: '100px', color: '#8a8a8a' }}
          />
        ) : (
          <Row gutter={[16, 16]}>
            {documents.map((doc) => (
              <Col xs={24} sm={12} md={8} lg={6} key={doc.id}>
                <div
                  onClick={() => handleDocumentClick(doc)}
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    padding: '16px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    height: '140px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(139, 92, 246, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  {/* 文档图标 */}
                  <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '12px' }}>
                    {doc.content_type === 'markdown' ? (
                      <FileMarkdownOutlined style={{ fontSize: '20px', color: '#8b5cf6', marginRight: '8px' }} />
                    ) : (
                      <FileTextOutlined style={{ fontSize: '20px', color: '#52c41a', marginRight: '8px' }} />
                    )}
                    <div style={{ flex: 1 }}>
                      <h4 style={{ 
                        color: '#ffffff', 
                        margin: 0, 
                        fontSize: '16px', 
                        fontWeight: 500,
                        lineHeight: '1.4',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {doc.title}
                      </h4>
                    </div>
                  </div>
                  
                  {/* 标签 */}
                  <div style={{ marginBottom: '12px' }}>
                    <span style={{
                      background: doc.content_type === 'markdown' ? 'rgba(139, 92, 246, 0.2)' : 'rgba(82, 196, 26, 0.2)',
                      color: doc.content_type === 'markdown' ? '#8b5cf6' : '#52c41a',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>
                      {doc.content_type === 'markdown' ? 'Markdown' : '纯文本'}
                    </span>
                  </div>
                  
                  {/* 底部信息 */}
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    fontSize: '12px',
                    color: '#8a8a8a'
                  }}>
                    <span>
                      <CalendarOutlined style={{ marginRight: '4px' }} />
                      {formatDate(doc.updated_at)}
                    </span>
                    <span>
                      <EyeOutlined style={{ marginRight: '4px' }} />
                      {doc.view_count}
                    </span>
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        )}
      </div>

      {/* AI助手面板 */}
      <AIAssistantPanel />
    </div>
  );
};

export default HomePage;
