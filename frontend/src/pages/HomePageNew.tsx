import React, { useEffect } from 'react';
import { Row, Col, Button, Space, Empty } from 'antd';
import {
  FileTextOutlined,
  FileMarkdownOutlined,
  EyeOutlined,
  CalendarOutlined,
  RobotOutlined,
  SettingOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  BulbOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { DocumentListItem } from '../types';
import { useDocumentStore } from '../store/documentStore';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { documents, loading, fetchDocuments } = useDocumentStore();

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const handleDocumentClick = (doc: DocumentListItem) => {
    navigate(`/document/${doc.id}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/recent') return '最近';
    if (path === '/favorites') return '收藏';
    if (path.startsWith('/category/')) return '分类文档';
    return '全部笔记';
  };

  // AI助手面板组件
  const AIAssistantPanel = () => (
    <div style={{
      width: '320px',
      background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      padding: '24px',
      borderRadius: '16px',
      margin: '24px',
      color: 'white',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: '-50px',
        right: '-50px',
        width: '100px',
        height: '100px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '50%',
      }} />
      
      <div style={{ position: 'relative', zIndex: 1 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <RobotOutlined style={{ fontSize: '24px', marginRight: '8px' }} />
          <span style={{ fontSize: '18px', fontWeight: 600 }}>AI助手</span>
        </div>
        
        <p style={{ 
          fontSize: '14px', 
          lineHeight: '1.5', 
          marginBottom: '20px',
          opacity: 0.9 
        }}>
          AI帮助您整理和优化笔记内容
        </p>
        
        <Button 
          type="primary" 
          size="large"
          style={{
            width: '100%',
            background: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '8px',
            marginBottom: '24px'
          }}
        >
          开始使用
        </Button>
        
        <div style={{ marginBottom: '24px' }}>
          <h4 style={{ color: 'white', marginBottom: '12px', fontSize: '16px' }}>快捷工具</h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
            <div style={{ textAlign: 'center' }}>
              <ImportOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>导入</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <ExportOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>分享</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <SettingOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>优化</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <DeleteOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
              <div style={{ fontSize: '12px' }}>回收站</div>
            </div>
          </div>
        </div>
        
        <div>
          <h4 style={{ color: 'white', marginBottom: '12px', fontSize: '16px' }}>支持格式</h4>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              background: 'rgba(255, 255, 255, 0.15)', 
              padding: '8px 12px', 
              borderRadius: '6px',
              marginBottom: '8px',
              fontSize: '14px'
            }}>
              <FileMarkdownOutlined style={{ marginRight: '8px' }} />
              Markdown
              <div style={{ fontSize: '12px', opacity: 0.8 }}>支持标准 Markdown 语法</div>
            </div>
            <div style={{ 
              background: 'rgba(255, 255, 255, 0.15)', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px'
            }}>
              <FileTextOutlined style={{ marginRight: '8px' }} />
              纯文本
              <div style={{ fontSize: '12px', opacity: 0.8 }}>简单易用的文本格式</div>
            </div>
          </div>
        </div>
        
        <div style={{ 
          background: 'rgba(255, 255, 255, 0.1)', 
          padding: '12px', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <BulbOutlined style={{ fontSize: '20px', marginBottom: '8px' }} />
          <div style={{ fontSize: '14px', fontWeight: 500 }}>帮助中心</div>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>遇到问题？查看使用指南和常见问题</div>
          <Button 
            size="small" 
            style={{ 
              marginTop: '8px',
              background: 'transparent',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              color: 'white'
            }}
          >
            查看帮助
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div style={{ display: 'flex', height: '100%', background: '#1a1a2e' }}>
      {/* 主内容区域 */}
      <div style={{ flex: 1, padding: '24px' }}>
        {/* 页面标题和工具栏 */}
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ color: '#ffffff', margin: 0, fontSize: '24px', fontWeight: 600 }}>
            {getPageTitle()}
          </h2>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button 
              icon={<EyeOutlined />}
              style={{ 
                background: 'rgba(139, 92, 246, 0.2)', 
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#8b5cf6'
              }}
            >
              网格视图
            </Button>
            <Button 
              icon={<FileTextOutlined />}
              style={{ 
                background: 'rgba(255, 255, 255, 0.1)', 
                border: '1px solid rgba(255, 255, 255, 0.2)',
                color: '#ffffff'
              }}
            >
              列表视图
            </Button>
          </div>
        </div>

        {/* 文档网格 */}
        {documents.length === 0 ? (
          <Empty
            description="暂无文档"
            style={{ marginTop: '100px', color: '#8a8a8a' }}
          />
        ) : (
          <Row gutter={[16, 16]}>
            {documents.map((doc) => (
              <Col xs={24} sm={12} md={8} lg={6} key={doc.id}>
                <div
                  onClick={() => handleDocumentClick(doc)}
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    padding: '16px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    height: '140px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(139, 92, 246, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  {/* 文档图标 */}
                  <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '12px' }}>
                    {doc.content_type === 'markdown' ? (
                      <FileMarkdownOutlined style={{ fontSize: '20px', color: '#8b5cf6', marginRight: '8px' }} />
                    ) : (
                      <FileTextOutlined style={{ fontSize: '20px', color: '#52c41a', marginRight: '8px' }} />
                    )}
                    <div style={{ flex: 1 }}>
                      <h4 style={{ 
                        color: '#ffffff', 
                        margin: 0, 
                        fontSize: '16px', 
                        fontWeight: 500,
                        lineHeight: '1.4',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {doc.title}
                      </h4>
                    </div>
                  </div>
                  
                  {/* 标签 */}
                  <div style={{ marginBottom: '12px' }}>
                    <span style={{
                      background: doc.content_type === 'markdown' ? 'rgba(139, 92, 246, 0.2)' : 'rgba(82, 196, 26, 0.2)',
                      color: doc.content_type === 'markdown' ? '#8b5cf6' : '#52c41a',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>
                      {doc.content_type === 'markdown' ? 'Markdown' : '纯文本'}
                    </span>
                  </div>
                  
                  {/* 底部信息 */}
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    fontSize: '12px',
                    color: '#8a8a8a'
                  }}>
                    <span>
                      <CalendarOutlined style={{ marginRight: '4px' }} />
                      {formatDate(doc.updated_at)}
                    </span>
                    <span>
                      <EyeOutlined style={{ marginRight: '4px' }} />
                      {doc.view_count}
                    </span>
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        )}
      </div>

      {/* AI助手面板 */}
      <AIAssistantPanel />
    </div>
  );
};

export default HomePage;
