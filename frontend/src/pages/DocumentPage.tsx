import React, { useState, useEffect } from 'react';
import { Button, Input, Select, Tag, Space, message, Spin } from 'antd';
import { SaveOutlined, EyeOutlined, EditOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import { marked } from 'marked';
import { Document } from '@/types';

const { Option } = Select;

// 模拟文档数据
const mockDocument: Document = {
  id: 1,
  title: '项目开发文档',
  content: `# 项目开发文档

## 项目概述
这是一个基于React和Go的云笔记系统。

## 技术栈
- 前端: React + TypeScript + Ant Design
- 后端: Go + Gin + GORM
- 数据库: PostgreSQL
- 存储: MinIO

## 功能特性
1. 用户认证和授权
2. 文档的增删改查
3. 分类管理
4. 标签系统
5. 搜索功能

## 开发进度
- [x] 用户认证
- [x] 基础布局
- [ ] 文档编辑器
- [ ] 文件上传
- [ ] 搜索功能

## 注意事项
请确保在开发过程中遵循代码规范。`,
  content_type: 'markdown',
  category_id: 1,
  is_favorite: false,
  is_recent: true,
  view_count: 25,
  created_at: '2024-01-20T10:30:00Z',
  updated_at: '2024-01-20T15:45:00Z',
  tags: [
    { id: 1, name: '工作', color: '#108ee9', created_at: '', updated_at: '', doc_count: 0 },
    { id: 2, name: '技术', color: '#87d068', created_at: '', updated_at: '', doc_count: 0 },
  ],
};

const DocumentPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isPreview, setIsPreview] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [contentType, setContentType] = useState<'markdown' | 'txt'>('markdown');

  useEffect(() => {
    loadDocument();
  }, [id]);

  const loadDocument = async () => {
    setLoading(true);
    try {
      // TODO: 从API加载文档
      // const doc = await apiService.getDocument(parseInt(id!));
      const doc = mockDocument;
      setDocument(doc);
      setTitle(doc.title);
      setContent(doc.content);
      setContentType(doc.content_type);
    } catch (error) {
      message.error('加载文档失败');
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      message.error('请输入文档标题');
      return;
    }

    setSaving(true);
    try {
      // TODO: 保存文档到API
      // await apiService.updateDocument(parseInt(id!), {
      //   title,
      //   content,
      //   content_type: contentType,
      // });
      
      setDocument(prev => prev ? {
        ...prev,
        title,
        content,
        content_type: contentType,
        updated_at: new Date().toISOString(),
      } : null);
      
      message.success('保存成功');
      setIsEditing(false);
    } catch (error) {
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  const renderPreview = () => {
    if (contentType === 'markdown') {
      return (
        <div
          className="markdown-preview"
          dangerouslySetInnerHTML={{ __html: marked(content) }}
          style={{
            padding: '20px',
            background: 'rgba(26, 26, 46, 0.6)',
            borderRadius: '8px',
            color: '#ffffff',
            lineHeight: '1.6',
            minHeight: '400px',
          }}
        />
      );
    } else {
      return (
        <div
          style={{
            padding: '20px',
            background: 'rgba(26, 26, 46, 0.6)',
            borderRadius: '8px',
            color: '#ffffff',
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace',
            lineHeight: '1.6',
            minHeight: '400px',
          }}
        >
          {content}
        </div>
      );
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!document) {
    return <div>文档不存在</div>;
  }

  return (
    <div style={{ padding: '24px', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/')}
          >
            返回
          </Button>
          
          {isEditing ? (
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              style={{ fontSize: '20px', fontWeight: 'bold', maxWidth: '400px' }}
              placeholder="输入文档标题"
            />
          ) : (
            <h1 style={{ color: '#ffffff', margin: 0, fontSize: '24px' }}>
              {document.title}
            </h1>
          )}

          {document.tags && document.tags.length > 0 && (
            <Space>
              {document.tags.map(tag => (
                <Tag key={tag.id} color={tag.color}>
                  {tag.name}
                </Tag>
              ))}
            </Space>
          )}
        </div>

        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          {isEditing && (
            <Select
              value={contentType}
              onChange={setContentType}
              style={{ width: 120 }}
            >
              <Option value="markdown">Markdown</Option>
              <Option value="txt">纯文本</Option>
            </Select>
          )}

          <Button
            icon={isPreview ? <EditOutlined /> : <EyeOutlined />}
            onClick={() => setIsPreview(!isPreview)}
          >
            {isPreview ? '编辑' : '预览'}
          </Button>

          {isEditing ? (
            <Space>
              <Button onClick={() => setIsEditing(false)}>
                取消
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={saving}
                onClick={handleSave}
              >
                保存
              </Button>
            </Space>
          ) : (
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
            >
              编辑
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        {isPreview ? (
          renderPreview()
        ) : isEditing ? (
          <Editor
            height="100%"
            language={contentType === 'markdown' ? 'markdown' : 'plaintext'}
            value={content}
            onChange={(value) => setContent(value || '')}
            theme="vs-dark"
            options={{
              fontSize: 14,
              lineHeight: 1.6,
              wordWrap: 'on',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              automaticLayout: true,
            }}
          />
        ) : (
          renderPreview()
        )}
      </div>
    </div>
  );
};

export default DocumentPage;
