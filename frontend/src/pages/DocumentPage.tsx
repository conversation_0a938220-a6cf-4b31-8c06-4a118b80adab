import React, { useState, useEffect } from 'react';
import { Button, Input, Select, Tag, Space, message, Spin } from 'antd';
import { SaveOutlined, EyeOutlined, EditOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import { marked } from 'marked';
import { Document } from '../types';
import { useDocumentStore } from '../store/documentStore';

const { Option } = Select;

const DocumentPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    currentDocument,
    loading,
    fetchDocument,
    updateDocument,
    clearCurrentDocument
  } = useDocumentStore();

  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isPreview, setIsPreview] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [contentType, setContentType] = useState<'markdown' | 'txt'>('markdown');

  useEffect(() => {
    if (id) {
      loadDocument();
    }
    return () => {
      clearCurrentDocument();
    };
  }, [id]);

  useEffect(() => {
    if (currentDocument) {
      setTitle(currentDocument.title);
      setContent(currentDocument.content);
      setContentType(currentDocument.content_type);
    }
  }, [currentDocument]);

  const loadDocument = async () => {
    try {
      await fetchDocument(parseInt(id!));
    } catch (error) {
      message.error('加载文档失败');
      navigate('/');
    }
  };

  const handleSave = async () => {
    if (!title.trim()) {
      message.error('请输入文档标题');
      return;
    }

    setSaving(true);
    try {
      await updateDocument(parseInt(id!), {
        title,
        content,
        content_type: contentType,
      });

      message.success('保存成功');
      setIsEditing(false);
    } catch (error) {
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  const renderPreview = () => {
    if (contentType === 'markdown') {
      return (
        <div
          className="markdown-preview"
          dangerouslySetInnerHTML={{ __html: marked(content) }}
          style={{
            padding: '20px',
            background: 'rgba(26, 26, 46, 0.6)',
            borderRadius: '8px',
            color: '#ffffff',
            lineHeight: '1.6',
            minHeight: '400px',
          }}
        />
      );
    } else {
      return (
        <div
          style={{
            padding: '20px',
            background: 'rgba(26, 26, 46, 0.6)',
            borderRadius: '8px',
            color: '#ffffff',
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace',
            lineHeight: '1.6',
            minHeight: '400px',
          }}
        >
          {content}
        </div>
      );
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!currentDocument) {
    return <div>文档不存在</div>;
  }

  return (
    <div style={{ padding: '24px', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/')}
          >
            返回
          </Button>
          
          {isEditing ? (
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              style={{ fontSize: '20px', fontWeight: 'bold', maxWidth: '400px' }}
              placeholder="输入文档标题"
            />
          ) : (
            <h1 style={{ color: '#ffffff', margin: 0, fontSize: '24px' }}>
              {currentDocument.title}
            </h1>
          )}

          {currentDocument.tags && currentDocument.tags.length > 0 && (
            <Space>
              {currentDocument.tags.map(tag => (
                <Tag key={tag.id} color={tag.color}>
                  {tag.name}
                </Tag>
              ))}
            </Space>
          )}
        </div>

        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          {isEditing && (
            <Select
              value={contentType}
              onChange={setContentType}
              style={{ width: 120 }}
            >
              <Option value="markdown">Markdown</Option>
              <Option value="txt">纯文本</Option>
            </Select>
          )}

          <Button
            icon={isPreview ? <EditOutlined /> : <EyeOutlined />}
            onClick={() => setIsPreview(!isPreview)}
          >
            {isPreview ? '编辑' : '预览'}
          </Button>

          {isEditing ? (
            <Space>
              <Button onClick={() => setIsEditing(false)}>
                取消
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={saving}
                onClick={handleSave}
              >
                保存
              </Button>
            </Space>
          ) : (
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
            >
              编辑
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        {isPreview ? (
          renderPreview()
        ) : isEditing ? (
          <Editor
            height="100%"
            language={contentType === 'markdown' ? 'markdown' : 'plaintext'}
            value={content}
            onChange={(value) => setContent(value || '')}
            theme="vs-dark"
            options={{
              fontSize: 14,
              lineHeight: 1.6,
              wordWrap: 'on',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              automaticLayout: true,
            }}
          />
        ) : (
          renderPreview()
        )}
      </div>
    </div>
  );
};

export default DocumentPage;
