import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Row, Col, Card, Tag, Button, Dropdown, Space, Empty, Spin, Tabs } from 'antd';
import {
  FileTextOutlined,
  FileMarkdownOutlined,
  MoreOutlined,
  StarOutlined,
  StarFilled,
  EyeOutlined,
  CalendarOutlined,
  FolderOutlined,
  TagOutlined,
} from '@ant-design/icons';
import { DocumentListItem, Category, Tag as TagType } from '@/types';
import apiService from '@/services/api';
import type { MenuProps } from 'antd';

const { Meta } = Card;
const { TabPane } = Tabs;

interface SearchResult {
  query: string;
  documents: DocumentListItem[];
  categories: Category[];
  tags: TagType[];
  total: {
    documents: number;
    categories: number;
    tags: number;
  };
}

const SearchPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<SearchResult | null>(null);
  const query = searchParams.get('q') || '';

  useEffect(() => {
    if (query) {
      performSearch(query);
    }
  }, [query]);

  const performSearch = async (searchQuery: string) => {
    setLoading(true);
    try {
      const result = await apiService.search(searchQuery);
      setSearchResult(result);
    } catch (error) {
      console.error('搜索失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentClick = (doc: DocumentListItem) => {
    navigate(`/document/${doc.id}`);
  };

  const handleCategoryClick = (category: Category) => {
    navigate(`/category/${category.id}`);
  };

  const getDocumentMenuItems = (doc: DocumentListItem): MenuProps['items'] => [
    {
      key: 'edit',
      label: '编辑',
      onClick: () => navigate(`/document/${doc.id}`),
    },
    {
      key: 'favorite',
      label: doc.is_favorite ? '取消收藏' : '收藏',
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      label: '删除',
      danger: true,
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!query) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Empty description="请输入搜索关键词" />
      </div>
    );
  }

  if (!searchResult) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Empty description="搜索出错，请重试" />
      </div>
    );
  }

  const { documents, categories, tags, total } = searchResult;
  const hasResults = total.documents > 0 || total.categories > 0 || total.tags > 0;

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <h2 style={{ color: '#ffffff', margin: 0, fontSize: '24px' }}>
          搜索结果: "{query}"
        </h2>
        <p style={{ color: '#a0a0a0', marginTop: '8px' }}>
          找到 {total.documents} 个文档，{total.categories} 个分类，{total.tags} 个标签
        </p>
      </div>

      {!hasResults ? (
        <Empty description="没有找到相关内容" />
      ) : (
        <Tabs defaultActiveKey="documents">
          <TabPane 
            tab={`文档 (${total.documents})`} 
            key="documents"
            icon={<FileTextOutlined />}
          >
            {documents.length === 0 ? (
              <Empty description="没有找到相关文档" />
            ) : (
              <Row gutter={[16, 16]}>
                {documents.map((doc) => (
                  <Col xs={24} sm={12} md={8} lg={6} key={doc.id}>
                    <Card
                      className="document-card"
                      hoverable
                      onClick={() => handleDocumentClick(doc)}
                      actions={[
                        <Button
                          type="text"
                          icon={doc.is_favorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                        />,
                        <Space>
                          <EyeOutlined />
                          {doc.view_count}
                        </Space>,
                        <Dropdown
                          menu={{ items: getDocumentMenuItems(doc) }}
                          trigger={['click']}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button type="text" icon={<MoreOutlined />} />
                        </Dropdown>,
                      ]}
                    >
                      <Meta
                        avatar={
                          doc.content_type === 'markdown' ? (
                            <FileMarkdownOutlined style={{ fontSize: '24px', color: '#8b5cf6' }} />
                          ) : (
                            <FileTextOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                          )
                        }
                        title={doc.title}
                        description={
                          <div>
                            <div style={{ marginBottom: '8px' }}>
                              <Tag color={doc.content_type === 'markdown' ? 'purple' : 'green'}>
                                {doc.content_type === 'markdown' ? 'Markdown' : '纯文本'}
                              </Tag>
                            </div>
                            <div className="document-meta">
                              <span>
                                <CalendarOutlined style={{ marginRight: '4px' }} />
                                {formatDate(doc.updated_at)}
                              </span>
                            </div>
                          </div>
                        }
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </TabPane>

          <TabPane 
            tab={`分类 (${total.categories})`} 
            key="categories"
            icon={<FolderOutlined />}
          >
            {categories.length === 0 ? (
              <Empty description="没有找到相关分类" />
            ) : (
              <Row gutter={[16, 16]}>
                {categories.map((category) => (
                  <Col xs={24} sm={12} md={8} lg={6} key={category.id}>
                    <Card
                      className="document-card"
                      hoverable
                      onClick={() => handleCategoryClick(category)}
                    >
                      <Meta
                        avatar={
                          <FolderOutlined 
                            style={{ 
                              fontSize: '24px', 
                              color: category.color || '#1890ff' 
                            }} 
                          />
                        }
                        title={category.name}
                        description={`${category.doc_count || 0} 个文档`}
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </TabPane>

          <TabPane 
            tab={`标签 (${total.tags})`} 
            key="tags"
            icon={<TagOutlined />}
          >
            {tags.length === 0 ? (
              <Empty description="没有找到相关标签" />
            ) : (
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {tags.map((tag) => (
                  <Tag
                    key={tag.id}
                    color={tag.color}
                    style={{ 
                      padding: '4px 12px', 
                      fontSize: '14px',
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      // TODO: 导航到标签页面
                      console.log('点击标签:', tag.name);
                    }}
                  >
                    {tag.name} ({tag.doc_count})
                  </Tag>
                ))}
              </div>
            )}
          </TabPane>
        </Tabs>
      )}
    </div>
  );
};

export default SearchPage;
