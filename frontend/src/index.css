body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Markdown预览样式 */
.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  color: #8b5cf6;
  margin-top: 24px;
  margin-bottom: 16px;
}

.markdown-preview h1 {
  font-size: 2em;
  border-bottom: 1px solid rgba(139, 92, 246, 0.3);
  padding-bottom: 8px;
}

.markdown-preview h2 {
  font-size: 1.5em;
}

.markdown-preview h3 {
  font-size: 1.25em;
}

.markdown-preview p {
  margin-bottom: 16px;
  line-height: 1.6;
}

.markdown-preview ul,
.markdown-preview ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.markdown-preview li {
  margin-bottom: 4px;
}

.markdown-preview blockquote {
  border-left: 4px solid #8b5cf6;
  padding-left: 16px;
  margin: 16px 0;
  color: #a0a0a0;
  font-style: italic;
}

.markdown-preview code {
  background: rgba(139, 92, 246, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #8b5cf6;
}

.markdown-preview pre {
  background: rgba(0, 0, 0, 0.3);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-preview pre code {
  background: none;
  padding: 0;
  color: #ffffff;
}

.markdown-preview table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.markdown-preview th,
.markdown-preview td {
  border: 1px solid rgba(139, 92, 246, 0.3);
  padding: 8px 12px;
  text-align: left;
}

.markdown-preview th {
  background: rgba(139, 92, 246, 0.1);
  font-weight: bold;
}

.markdown-preview a {
  color: #8b5cf6;
  text-decoration: none;
}

.markdown-preview a:hover {
  text-decoration: underline;
}
