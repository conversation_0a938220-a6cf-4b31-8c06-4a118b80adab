// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

// 分类相关类型
export interface Category {
  id: number;
  name: string;
  icon?: string;
  color?: string;
  parent_id?: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
  children?: Category[];
  doc_count: number;
}

export interface CategoryRequest {
  name: string;
  icon?: string;
  color?: string;
  parent_id?: number;
  sort_order?: number;
}

// 文档相关类型
export interface Document {
  id: number;
  title: string;
  content: string;
  content_type: 'markdown' | 'txt';
  category_id?: number;
  category?: Category;
  is_favorite: boolean;
  is_recent: boolean;
  view_count: number;
  created_at: string;
  updated_at: string;
  tags?: Tag[];
}

export interface DocumentListItem {
  id: number;
  title: string;
  content_type: 'markdown' | 'txt';
  category_id?: number;
  is_favorite: boolean;
  is_recent: boolean;
  view_count: number;
  created_at: string;
  updated_at: string;
}

export interface DocumentRequest {
  title: string;
  content: string;
  content_type: 'markdown' | 'txt';
  category_id?: number;
  tag_ids?: number[];
}

// 标签相关类型
export interface Tag {
  id: number;
  name: string;
  color?: string;
  created_at: string;
  updated_at: string;
  doc_count: number;
}

export interface TagRequest {
  name: string;
  color?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// 应用状态类型
export interface AppState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  categories: Category[];
  documents: DocumentListItem[];
  tags: Tag[];
  currentDocument: Document | null;
  loading: boolean;
  error: string | null;
}

// 侧边栏菜单项类型
export interface SidebarMenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: SidebarMenuItem[];
  type?: 'category' | 'system';
  count?: number;
}
