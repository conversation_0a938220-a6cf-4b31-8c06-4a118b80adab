import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { DocumentRequest } from '@/types';
import { useDocumentStore } from '@/store/documentStore';
import { useCategoryStore } from '@/store/categoryStore';

const { Option } = Select;
const { TextArea } = Input;

interface CreateDocumentModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (documentId: number) => void;
}

const CreateDocumentModal: React.FC<CreateDocumentModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { createDocument } = useDocumentStore();
  const { categories, fetchCategories } = useCategoryStore();

  useEffect(() => {
    if (visible) {
      fetchCategories().catch(() => {
        // 忽略错误，分类是可选的
      });
    }
  }, [visible, fetchCategories]);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const documentData: DocumentRequest = {
        title: values.title,
        content: values.content || '',
        content_type: values.content_type || 'markdown',
        category_id: values.category_id || undefined,
      };

      const document = await createDocument(documentData);
      message.success('文档创建成功');
      form.resetFields();
      onSuccess(document.id);
    } catch (error) {
      message.error('创建文档失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="新建文档"
      open={visible}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          content_type: 'markdown',
        }}
      >
        <Form.Item
          name="title"
          label="文档标题"
          rules={[
            { required: true, message: '请输入文档标题' },
            { max: 255, message: '标题长度不能超过255个字符' },
          ]}
        >
          <Input placeholder="请输入文档标题" />
        </Form.Item>

        <Form.Item
          name="content_type"
          label="文档类型"
        >
          <Select>
            <Option value="markdown">Markdown</Option>
            <Option value="txt">纯文本</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="category_id"
          label="分类"
        >
          <Select placeholder="选择分类（可选）" allowClear>
            {categories.map(category => (
              <Option key={category.id} value={category.id}>
                {category.name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="content"
          label="初始内容"
        >
          <TextArea
            rows={6}
            placeholder="可以输入一些初始内容（可选）"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateDocumentModal;
