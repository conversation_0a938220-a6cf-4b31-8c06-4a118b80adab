import React from 'react';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 'medium', showText = true, className = '' }) => {
  const sizeMap = {
    small: { width: 24, height: 24, fontSize: '12px', textSize: '14px' },
    medium: { width: 32, height: 32, fontSize: '16px', textSize: '18px' },
    large: { width: 48, height: 48, fontSize: '24px', textSize: '24px' },
  };

  const { width, height, fontSize, textSize } = sizeMap[size];

  return (
    <div className={`logo-container ${className}`} style={{ display: 'flex', alignItems: 'center', gap: showText ? '12px' : '0' }}>
      {/* Logo图标 */}
      <div
        className="logo-icon"
        style={{
          width: `${width}px`,
          height: `${height}px`,
          background: 'linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%)',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
          fontSize: fontSize,
          fontFamily: 'Arial, sans-serif',
          boxShadow: '0 4px 12px rgba(139, 92, 246, 0.3)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* 背景装饰 */}
        <div
          style={{
            position: 'absolute',
            top: '-50%',
            right: '-50%',
            width: '100%',
            height: '100%',
            background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%)',
            borderRadius: '50%',
          }}
        />
        
        {/* 主要文字 */}
        <span style={{ position: 'relative', zIndex: 1 }}>JJ</span>
      </div>

      {/* Logo文字 */}
      {showText && (
        <div
          className="logo-text"
          style={{
            color: '#ffffff',
            fontSize: textSize,
            fontWeight: '600',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
            background: 'linear-gradient(135deg, #8b5cf6, #a855f7)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
          }}
        >
          JiangJiangNote
        </div>
      )}
    </div>
  );
};

export default Logo;
