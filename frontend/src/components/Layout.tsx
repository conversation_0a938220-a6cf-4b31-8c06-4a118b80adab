import React, { useState, useEffect } from 'react';
import { Layout as AntLayout, Menu, Input, Button, Dropdown, Avatar } from 'antd';
import {
  FileTextOutlined,
  FolderOutlined,
  StarOutlined,
  ClockCircleOutlined,
  TagOutlined,
  SearchOutlined,
  PlusOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useCategoryStore } from '../store/categoryStore';
import Logo from './Logo';
import CreateDocumentModal from './CreateDocumentModal';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = AntLayout;
const { Search } = Input;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();
  const { categories = [], fetchCategories } = useCategoryStore();
  const [collapsed, setCollapsed] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);

  useEffect(() => {
    fetchCategories().catch((error) => {
      console.warn('分类加载失败，使用空列表:', error);
      // 忽略错误，分类加载失败不影响主要功能
    });
  }, [fetchCategories]);

  // 确保 categories 是数组
  const safeCategories = Array.isArray(categories) ? categories : [];

  // 侧边栏菜单项
  const sidebarItems: MenuProps['items'] = [
    {
      key: 'all',
      icon: <FileTextOutlined />,
      label: '全部笔记',
      onClick: () => navigate('/'),
    },
    {
      key: 'recent',
      icon: <ClockCircleOutlined />,
      label: '最近',
      onClick: () => navigate('/recent'),
    },
    {
      key: 'favorites',
      icon: <StarOutlined />,
      label: '收藏',
      onClick: () => navigate('/favorites'),
    },
    {
      type: 'divider',
    },
    {
      key: 'categories',
      icon: <FolderOutlined />,
      label: '文档分类',
      children: safeCategories.map(category => ({
        key: `category-${category.id}`,
        label: `${category.name} (${category.doc_count || 0})`,
        icon: <FolderOutlined style={{ color: category.color || '#1890ff' }} />,
        onClick: () => navigate(`/category/${category.id}`),
      })),
    },
    {
      type: 'divider',
    },
    {
      key: 'tags',
      icon: <TagOutlined />,
      label: '标签',
      children: [
        {
          key: 'tag-work',
          label: '工作',
        },
        {
          key: 'tag-study',
          label: '学习',
        },
        {
          key: 'tag-life',
          label: '生活',
        },
        {
          key: 'tag-tech',
          label: '技术',
        },
        {
          key: 'tag-design',
          label: '设计',
        },
      ],
    },
  ];

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        navigate('/login');
      },
    },
  ];

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path === '/') return ['all'];
    if (path === '/recent') return ['recent'];
    if (path === '/favorites') return ['favorites'];
    if (path.startsWith('/category/')) {
      const categoryId = path.split('/')[2];
      return [categoryId];
    }
    return ['all'];
  };

  return (
    <AntLayout style={{ minHeight: '100vh', background: '#1a1a2e' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={280}
        collapsedWidth={64}
        style={{
          background: 'rgba(26, 26, 46, 0.95)',
          borderRight: '1px solid rgba(255, 255, 255, 0.08)',
          boxShadow: '2px 0 8px rgba(0, 0, 0, 0.15)',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100,
          overflow: 'auto',
        }}
      >
        {/* 侧边栏头部 */}
        <div style={{
          padding: '20px 16px',
          borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start'
        }}>
          <Logo size="medium" showText={!collapsed} />
        </div>

        {/* 新建按钮 */}
        <div style={{ padding: '16px' }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            block
            size="large"
            onClick={() => setCreateModalVisible(true)}
            style={{
              background: 'linear-gradient(135deg, #8b5cf6, #a855f7)',
              border: 'none',
              borderRadius: '12px',
              height: '44px',
              fontWeight: '600',
              fontSize: '14px',
              boxShadow: '0 4px 12px rgba(139, 92, 246, 0.3)',
              transition: 'all 0.3s ease',
            }}
          >
            {!collapsed && '新建文档'}
          </Button>
        </div>

        {/* 侧边栏菜单 */}
        <Menu
          mode="inline"
          selectedKeys={getSelectedKeys()}
          items={sidebarItems}
          style={{
            background: 'transparent',
            border: 'none',
            padding: '0 8px',
          }}
        />
      </Sider>

      {/* 主内容区域 */}
      <AntLayout style={{
        marginLeft: collapsed ? 64 : 280,
        transition: 'margin-left 0.2s',
        background: '#1a1a2e'
      }}>
        {/* 头部 */}
        <Header style={{
          background: 'rgba(26, 26, 46, 0.95)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
          padding: '0 24px',
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backdropFilter: 'blur(10px)',
        }}>
          {/* 左侧：搜索框 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Search
              placeholder="搜索笔记..."
              allowClear
              enterButton={<SearchOutlined />}
              size="middle"
              onSearch={(value) => {
                if (value.trim()) {
                  navigate(`/search?q=${encodeURIComponent(value)}`);
                }
              }}
              style={{
                width: 320,
              }}
            />
          </div>

          {/* 右侧：操作按钮和用户菜单 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
              style={{
                background: 'linear-gradient(135deg, #8b5cf6, #a855f7)',
                border: 'none',
                borderRadius: '8px',
                height: '36px',
                fontWeight: '500',
                boxShadow: '0 2px 8px rgba(139, 92, 246, 0.3)',
              }}
            >
              新建
            </Button>

            <Button
              type="text"
              icon={<BellOutlined />}
              style={{
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: '16px',
                width: '36px',
                height: '36px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '8px',
                transition: 'background 0.2s',
                gap: '8px'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
              }}
              >
                <Avatar
                  size={32}
                  icon={<UserOutlined />}
                  style={{
                    background: 'linear-gradient(135deg, #8b5cf6, #a855f7)',
                    border: '2px solid rgba(255, 255, 255, 0.1)'
                  }}
                />
                <span style={{
                  color: '#ffffff',
                  fontWeight: '500',
                  fontSize: '14px'
                }}>
                  {user?.username || '用户'}
                </span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content style={{
          background: '#1a1a2e',
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto',
        }}>
          {children}
        </Content>
      </AntLayout>

      {/* 新建文档模态框 */}
      <CreateDocumentModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={(documentId) => {
          setCreateModalVisible(false);
          navigate(`/document/${documentId}`);
        }}
      />
    </AntLayout>
  );
};

export default Layout;
