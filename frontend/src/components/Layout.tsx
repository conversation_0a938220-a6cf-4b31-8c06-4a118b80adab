import React, { useState, useEffect } from 'react';
import { Layout as AntLayout, Menu, Input, Button, Dropdown, Avatar } from 'antd';
import {
  FileTextOutlined,
  FolderOutlined,
  StarOutlined,
  ClockCircleOutlined,
  TagOutlined,
  SearchOutlined,
  PlusOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useCategoryStore } from '../store/categoryStore';
import Logo from './Logo';
import CreateDocumentModal from './CreateDocumentModal';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = AntLayout;
const { Search } = Input;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();
  const { categories = [], fetchCategories } = useCategoryStore();
  const [collapsed, setCollapsed] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);

  useEffect(() => {
    fetchCategories().catch((error) => {
      console.warn('分类加载失败，使用空列表:', error);
      // 忽略错误，分类加载失败不影响主要功能
    });
  }, [fetchCategories]);

  // 确保 categories 是数组
  const safeCategories = Array.isArray(categories) ? categories : [];

  // 侧边栏菜单项
  const sidebarItems: MenuProps['items'] = [
    {
      key: 'all',
      icon: <FileTextOutlined />,
      label: '全部笔记',
      onClick: () => navigate('/'),
    },
    {
      key: 'recent',
      icon: <ClockCircleOutlined />,
      label: '最近',
      onClick: () => navigate('/recent'),
    },
    {
      key: 'favorites',
      icon: <StarOutlined />,
      label: '收藏',
      onClick: () => navigate('/favorites'),
    },
    {
      type: 'divider',
    },
    {
      key: 'categories',
      icon: <FolderOutlined />,
      label: '文档分类',
      children: safeCategories.map(category => ({
        key: `category-${category.id}`,
        label: `${category.name} (${category.doc_count || 0})`,
        icon: <FolderOutlined style={{ color: category.color || '#1890ff' }} />,
        onClick: () => navigate(`/category/${category.id}`),
      })),
    },
    {
      type: 'divider',
    },
    {
      key: 'tags',
      icon: <TagOutlined />,
      label: '标签',
      children: [
        {
          key: 'tag-work',
          label: '工作',
        },
        {
          key: 'tag-study',
          label: '学习',
        },
        {
          key: 'tag-life',
          label: '生活',
        },
        {
          key: 'tag-tech',
          label: '技术',
        },
        {
          key: 'tag-design',
          label: '设计',
        },
      ],
    },
  ];

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        navigate('/login');
      },
    },
  ];

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path === '/') return ['all'];
    if (path === '/recent') return ['recent'];
    if (path === '/favorites') return ['favorites'];
    if (path.startsWith('/category/')) {
      const categoryId = path.split('/')[2];
      return [categoryId];
    }
    return ['all'];
  };

  return (
    <AntLayout className="main-layout">
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={280}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        {/* 侧边栏头部 */}
        <div className="sidebar-header">
          <Logo size="medium" showText={!collapsed} />
        </div>

        {/* 侧边栏菜单 */}
        <div className="sidebar-menu">
          <Menu
            mode="inline"
            selectedKeys={getSelectedKeys()}
            items={sidebarItems}
            style={{ border: 'none' }}
          />
        </div>
      </Sider>

      {/* 主内容区域 */}
      <AntLayout style={{ marginLeft: collapsed ? 80 : 280, transition: 'margin-left 0.2s' }}>
        {/* 头部 */}
        <Header>
          <div className="header-content">
            {/* 搜索框 */}
            <div className="header-search">
              <Search
                placeholder="搜索笔记..."
                allowClear
                enterButton={<SearchOutlined />}
                size="middle"
                onSearch={(value) => {
                  if (value.trim()) {
                    navigate(`/search?q=${encodeURIComponent(value)}`);
                  }
                }}
              />
            </div>

            {/* 头部操作按钮 */}
            <div className="header-actions">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                新建
              </Button>

              <Button
                icon={<BellOutlined />}
                style={{ background: 'transparent' }}
              />

              {/* 用户头像和下拉菜单 */}
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Avatar
                    size="small"
                    src={user?.avatar_url}
                    icon={<UserOutlined />}
                    style={{ background: '#8b5cf6' }}
                  />
                  <span style={{ color: '#ffffff' }}>{user?.username}</span>
                </div>
              </Dropdown>
            </div>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content className="content-area">
          {children}
        </Content>
      </AntLayout>

      {/* 新建文档模态框 */}
      <CreateDocumentModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={(documentId) => {
          setCreateModalVisible(false);
          navigate(`/document/${documentId}`);
        }}
      />
    </AntLayout>
  );
};

export default Layout;
