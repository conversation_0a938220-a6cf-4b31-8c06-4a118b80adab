/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.App {
  height: 100vh;
  background: #1a1a2e;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.8);
}

/* 登录注册页面样式 */
.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

.auth-form {
  width: 400px;
  padding: 40px;
  background: rgba(26, 26, 46, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.auth-title {
  text-align: center;
  margin-bottom: 30px;
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
}

.auth-form .ant-form-item-label > label {
  color: #ffffff;
}

.auth-form .ant-input,
.auth-form .ant-input-password {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: #ffffff;
}

.auth-form .ant-input:focus,
.auth-form .ant-input-password:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.auth-form .ant-btn-primary {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  height: 40px;
  font-weight: 500;
}

.auth-form .ant-btn-primary:hover {
  background: linear-gradient(135deg, #7c3aed, #9333ea);
}

.auth-link {
  text-align: center;
  margin-top: 20px;
}

.auth-link a {
  color: #8b5cf6;
  text-decoration: none;
}

.auth-link a:hover {
  color: #a855f7;
}

/* 布局样式 */
.main-layout {
  height: 100vh;
}

.main-layout .ant-layout-sider {
  background: rgba(15, 15, 35, 0.95) !important;
  border-right: 1px solid rgba(139, 92, 246, 0.2);
}

.main-layout .ant-layout-header {
  background: rgba(26, 26, 46, 0.95) !important;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  padding: 0 24px;
}

.main-layout .ant-layout-content {
  background: rgba(15, 15, 35, 0.8) !important;
}

/* 侧边栏样式 */
.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.sidebar-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu .ant-menu {
  background: transparent !important;
  border: none !important;
}

.sidebar-menu .ant-menu-item {
  color: #a0a0a0 !important;
  margin: 4px 8px !important;
  border-radius: 8px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.sidebar-menu .ant-menu-item:hover {
  background: rgba(139, 92, 246, 0.1) !important;
  color: #ffffff !important;
}

.sidebar-menu .ant-menu-item-selected {
  background: rgba(139, 92, 246, 0.2) !important;
  color: #8b5cf6 !important;
}

.sidebar-menu .ant-menu-submenu-title {
  color: #a0a0a0 !important;
  margin: 4px 8px !important;
  border-radius: 8px !important;
  height: 40px !important;
  line-height: 40px !important;
}

.sidebar-menu .ant-menu-submenu-title:hover {
  background: rgba(139, 92, 246, 0.1) !important;
  color: #ffffff !important;
}

/* 头部样式 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-search {
  flex: 1;
  max-width: 400px;
  margin: 0 20px;
}

.header-search .ant-input-search {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 20px;
}

.header-search .ant-input {
  background: transparent;
  border: none;
  color: #ffffff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-actions .ant-btn {
  border: none;
  background: rgba(139, 92, 246, 0.2);
  color: #ffffff;
  border-radius: 8px;
}

.header-actions .ant-btn:hover {
  background: rgba(139, 92, 246, 0.3);
  color: #ffffff;
}

/* 内容区域样式 */
.content-area {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

.document-grid {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.document-card {
  background: rgba(26, 26, 46, 0.6) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease;
  cursor: pointer;
}

.document-card:hover {
  border-color: rgba(139, 92, 246, 0.5) !important;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.1) !important;
  transform: translateY(-2px);
}

.document-card .ant-card-meta-title {
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.document-card .ant-card-meta-description {
  color: #a0a0a0 !important;
}

.document-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

.document-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.document-tag {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-form {
    width: 90%;
    margin: 0 20px;
    padding: 30px 20px;
  }
  
  .header-search {
    display: none;
  }
  
  .document-grid {
    padding: 16px;
  }
}
