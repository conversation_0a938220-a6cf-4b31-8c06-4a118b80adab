/**
 * 用户认证状态管理
 *
 * 使用Zustand管理用户认证状态，包括：
 * - 用户登录/注册
 * - 用户信息存储
 * - 认证状态持久化
 * - 自动登录检查
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest, RegisterRequest } from '../types';
import apiService from '../services/api';

// 认证状态接口
interface AuthState {
  user: User | null;          // 当前登录用户信息
  token: string | null;       // JWT认证令牌
  isAuthenticated: boolean;   // 是否已认证
  loading: boolean;           // 加载状态
  error: string | null;       // 错误信息
}

// 认证操作接口
interface AuthActions {
  login: (data: LoginRequest) => Promise<void>;     // 用户登录
  register: (data: RegisterRequest) => Promise<void>; // 用户注册
  logout: () => void;                               // 用户登出
  clearError: () => void;                           // 清除错误信息
  loadUserFromStorage: () => void;                  // 从本地存储加载用户信息
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // 状态
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,
      error: null,

      // 动作
      login: async (data: LoginRequest) => {
        try {
          set({ loading: true, error: null });
          const response = await apiService.login(data);
          
          // 保存token到localStorage
          localStorage.setItem('token', response.token);
          
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            loading: false,
          });
        } catch (error: any) {
          set({
            error: error.response?.data?.error || '登录失败',
            loading: false,
          });
          throw error;
        }
      },

      register: async (data: RegisterRequest) => {
        try {
          set({ loading: true, error: null });
          const response = await apiService.register(data);
          
          // 保存token到localStorage
          localStorage.setItem('token', response.token);
          
          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            loading: false,
          });
        } catch (error: any) {
          set({
            error: error.response?.data?.error || '注册失败',
            loading: false,
          });
          throw error;
        }
      },

      logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      loadUserFromStorage: () => {
        const token = localStorage.getItem('token');
        const userStr = localStorage.getItem('user');
        
        if (token && userStr) {
          try {
            const user = JSON.parse(userStr);
            set({
              user,
              token,
              isAuthenticated: true,
            });
          } catch (error) {
            // 如果解析失败，清除存储
            localStorage.removeItem('token');
            localStorage.removeItem('user');
          }
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
