import { create } from 'zustand';
import { Category, CategoryRequest } from '@/types';
import apiService from '@/services/api';

interface CategoryState {
  categories: Category[];
  loading: boolean;
  error: string | null;
}

interface CategoryActions {
  fetchCategories: () => Promise<void>;
  createCategory: (data: CategoryRequest) => Promise<Category>;
  updateCategory: (id: number, data: CategoryRequest) => Promise<Category>;
  deleteCategory: (id: number) => Promise<void>;
  clearError: () => void;
}

export const useCategoryStore = create<CategoryState & CategoryActions>((set, get) => ({
  // 状态
  categories: [],
  loading: false,
  error: null,

  // 动作
  fetchCategories: async () => {
    try {
      set({ loading: true, error: null });
      const categories = await apiService.getCategories();
      set({
        categories,
        loading: false,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '获取分类列表失败',
        loading: false,
      });
      throw error;
    }
  },

  createCategory: async (data: CategoryRequest) => {
    try {
      set({ loading: true, error: null });
      const category = await apiService.createCategory(data);
      
      // 添加到分类列表
      const { categories } = get();
      set({
        categories: [...categories, category],
        loading: false,
      });
      
      return category;
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '创建分类失败',
        loading: false,
      });
      throw error;
    }
  },

  updateCategory: async (id: number, data: CategoryRequest) => {
    try {
      set({ loading: true, error: null });
      const category = await apiService.updateCategory(id, data);
      
      // 更新分类列表
      const { categories } = get();
      const updatedCategories = categories.map(cat =>
        cat.id === id ? category : cat
      );
      
      set({
        categories: updatedCategories,
        loading: false,
      });
      
      return category;
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '更新分类失败',
        loading: false,
      });
      throw error;
    }
  },

  deleteCategory: async (id: number) => {
    try {
      set({ loading: true, error: null });
      await apiService.deleteCategory(id);
      
      // 从分类列表中移除
      const { categories } = get();
      const filteredCategories = categories.filter(cat => cat.id !== id);
      
      set({
        categories: filteredCategories,
        loading: false,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '删除分类失败',
        loading: false,
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
