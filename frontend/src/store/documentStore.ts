import { create } from 'zustand';
import { DocumentListItem, Document, DocumentRequest } from '@/types';
import apiService from '@/services/api';

interface DocumentState {
  documents: DocumentListItem[];
  currentDocument: Document | null;
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

interface DocumentActions {
  fetchDocuments: (params?: {
    category_id?: number;
    page?: number;
    limit?: number;
    search?: string;
  }) => Promise<void>;
  fetchDocument: (id: number) => Promise<void>;
  createDocument: (data: DocumentRequest) => Promise<Document>;
  updateDocument: (id: number, data: DocumentRequest) => Promise<Document>;
  deleteDocument: (id: number) => Promise<void>;
  fetchRecentDocuments: () => Promise<void>;
  fetchFavoriteDocuments: () => Promise<void>;
  toggleFavorite: (id: number) => Promise<void>;
  clearError: () => void;
  clearCurrentDocument: () => void;
}

export const useDocumentStore = create<DocumentState & DocumentActions>((set, get) => ({
  // 状态
  documents: [],
  currentDocument: null,
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 20,

  // 动作
  fetchDocuments: async (params) => {
    try {
      set({ loading: true, error: null });
      const response = await apiService.getDocuments(params);
      
      if (Array.isArray(response)) {
        // 如果返回的是数组，说明是简单的文档列表
        set({
          documents: response,
          total: response.length,
          page: params?.page || 1,
          limit: params?.limit || 20,
          loading: false,
        });
      } else {
        // 如果返回的是对象，包含分页信息
        set({
          documents: response.documents || [],
          total: response.total || 0,
          page: response.page || 1,
          limit: response.limit || 20,
          loading: false,
        });
      }
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '获取文档列表失败',
        loading: false,
      });
      throw error;
    }
  },

  fetchDocument: async (id: number) => {
    try {
      set({ loading: true, error: null });
      const document = await apiService.getDocument(id);
      set({
        currentDocument: document,
        loading: false,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '获取文档详情失败',
        loading: false,
      });
      throw error;
    }
  },

  createDocument: async (data: DocumentRequest) => {
    try {
      set({ loading: true, error: null });
      const document = await apiService.createDocument(data);
      
      // 添加到文档列表
      const { documents } = get();
      set({
        documents: [document, ...documents],
        loading: false,
      });
      
      return document;
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '创建文档失败',
        loading: false,
      });
      throw error;
    }
  },

  updateDocument: async (id: number, data: DocumentRequest) => {
    try {
      set({ loading: true, error: null });
      const document = await apiService.updateDocument(id, data);
      
      // 更新文档列表
      const { documents } = get();
      const updatedDocuments = documents.map(doc =>
        doc.id === id ? { ...doc, ...document } : doc
      );
      
      set({
        documents: updatedDocuments,
        currentDocument: document,
        loading: false,
      });
      
      return document;
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '更新文档失败',
        loading: false,
      });
      throw error;
    }
  },

  deleteDocument: async (id: number) => {
    try {
      set({ loading: true, error: null });
      await apiService.deleteDocument(id);
      
      // 从文档列表中移除
      const { documents } = get();
      const filteredDocuments = documents.filter(doc => doc.id !== id);
      
      set({
        documents: filteredDocuments,
        currentDocument: null,
        loading: false,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '删除文档失败',
        loading: false,
      });
      throw error;
    }
  },

  fetchRecentDocuments: async () => {
    try {
      set({ loading: true, error: null });
      const documents = await apiService.getRecentDocuments();
      set({
        documents,
        total: documents.length,
        loading: false,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '获取最近文档失败',
        loading: false,
      });
      throw error;
    }
  },

  fetchFavoriteDocuments: async () => {
    try {
      set({ loading: true, error: null });
      const documents = await apiService.getFavoriteDocuments();
      set({
        documents,
        total: documents.length,
        loading: false,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.error || '获取收藏文档失败',
        loading: false,
      });
      throw error;
    }
  },

  toggleFavorite: async (id: number) => {
    try {
      // 先乐观更新UI
      const { documents } = get();
      const updatedDocuments = documents.map(doc =>
        doc.id === id ? { ...doc, is_favorite: !doc.is_favorite } : doc
      );
      set({ documents: updatedDocuments });

      // 调用API
      await apiService.toggleFavorite(id);
    } catch (error: any) {
      // 如果失败，回滚更新
      const { documents } = get();
      const revertedDocuments = documents.map(doc =>
        doc.id === id ? { ...doc, is_favorite: !doc.is_favorite } : doc
      );
      set({
        documents: revertedDocuments,
        error: error.response?.data?.error || '更新收藏状态失败',
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  clearCurrentDocument: () => {
    set({ currentDocument: null });
  },
}));
