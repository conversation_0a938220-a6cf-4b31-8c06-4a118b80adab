import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  LoginRequest, 
  RegisterRequest, 
  LoginResponse, 
  User,
  Category,
  CategoryRequest,
  Document,
  DocumentListItem,
  DocumentRequest,
  Tag,
  TagRequest
} from '@/types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || '/api',
      timeout: 10000,
    });

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理错误
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token过期或无效，清除本地存储并跳转到登录页
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // 认证相关API
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post<LoginResponse>('/auth/login', data);
    return response.data;
  }

  async register(data: RegisterRequest): Promise<LoginResponse> {
    const response = await this.api.post<LoginResponse>('/auth/register', data);
    return response.data;
  }

  async getProfile(): Promise<User> {
    const response = await this.api.get<User>('/auth/profile');
    return response.data;
  }

  // 分类相关API
  async getCategories(): Promise<Category[]> {
    const response = await this.api.get<Category[]>('/categories');
    return response.data;
  }

  async createCategory(data: CategoryRequest): Promise<Category> {
    const response = await this.api.post<Category>('/categories', data);
    return response.data;
  }

  async updateCategory(id: number, data: CategoryRequest): Promise<Category> {
    const response = await this.api.put<Category>(`/categories/${id}`, data);
    return response.data;
  }

  async deleteCategory(id: number): Promise<void> {
    await this.api.delete(`/categories/${id}`);
  }

  // 文档相关API
  async getDocuments(params?: {
    category_id?: number;
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<DocumentListItem[]> {
    const response = await this.api.get<DocumentListItem[]>('/documents', { params });
    return response.data;
  }

  async getDocument(id: number): Promise<Document> {
    const response = await this.api.get<Document>(`/documents/${id}`);
    return response.data;
  }

  async createDocument(data: DocumentRequest): Promise<Document> {
    const response = await this.api.post<Document>('/documents', data);
    return response.data;
  }

  async updateDocument(id: number, data: DocumentRequest): Promise<Document> {
    const response = await this.api.put<Document>(`/documents/${id}`, data);
    return response.data;
  }

  async deleteDocument(id: number): Promise<void> {
    await this.api.delete(`/documents/${id}`);
  }

  async getRecentDocuments(): Promise<DocumentListItem[]> {
    const response = await this.api.get<DocumentListItem[]>('/documents/recent');
    return response.data;
  }

  async getFavoriteDocuments(): Promise<DocumentListItem[]> {
    const response = await this.api.get<DocumentListItem[]>('/documents/favorites');
    return response.data;
  }

  async toggleFavorite(id: number): Promise<void> {
    await this.api.put(`/documents/${id}/favorite`);
  }

  // 标签相关API
  async getTags(): Promise<Tag[]> {
    const response = await this.api.get<Tag[]>('/tags');
    return response.data;
  }

  async createTag(data: TagRequest): Promise<Tag> {
    const response = await this.api.post<Tag>('/tags', data);
    return response.data;
  }

  async updateTag(id: number, data: TagRequest): Promise<Tag> {
    const response = await this.api.put<Tag>(`/tags/${id}`, data);
    return response.data;
  }

  async deleteTag(id: number): Promise<void> {
    await this.api.delete(`/tags/${id}`);
  }

  // 搜索API
  async search(query: string): Promise<any> {
    const response = await this.api.get('/search', {
      params: { q: query }
    });
    return response.data;
  }

  async searchDocuments(query: string): Promise<DocumentListItem[]> {
    const response = await this.api.get<DocumentListItem[]>('/search/documents', {
      params: { q: query }
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
