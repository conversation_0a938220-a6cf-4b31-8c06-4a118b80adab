# CloudNotes (江江笔记)

一款类似语雀的个人云笔记系统，支持Markdown和纯文本编辑。

## 项目概述

CloudNotes是一个现代化的知识管理平台，提供以下核心功能：

- 📝 支持Markdown和TXT格式的文档编辑
- 📁 文档分类管理（文件夹/知识库）
- 👀 实时预览和编辑
- 📎 文件上传和管理
- 🔍 全文搜索功能
- 🏷️ 标签管理
- 👤 用户认证和权限控制
- ⭐ 收藏和最近访问

## 技术栈

### 后端
- Go 1.24
- Gin Web框架
- GORM (ORM)
- PostgreSQL 15
- MinIO (文件存储)
- JWT (身份认证)

### 前端
- React 18
- TypeScript
- Ant Design 5.x
- React Router v6
- Monaco Editor
- Marked (Markdown解析)

## 项目结构

```
jiangjiangNote/
├── backend/                 # 后端Go服务
├── frontend/               # 前端React应用
├── docker-compose.yml      # 本地开发环境
└── README.md
```

## 快速开始

### 环境要求
- Go 1.24+
- Node.js 18+
- PostgreSQL 15+
- MinIO
- Docker Desktop (可选)

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd jiangjiangNote
```

2. 启动后端服务
```bash
cd backend
go mod tidy
go run cmd/server/main.go
```

3. 启动前端服务
```bash
cd frontend
npm install
npm start
```

4. 使用Docker (可选)
```bash
docker-compose up -d
```

## 开发规范

### Git提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### 代码规范
- 后端遵循Go官方代码规范
- 前端使用ESLint + Prettier
- 提交前运行代码检查和测试

## 许可证

MIT License
