# 修复前端导入路径脚本

Write-Host "修复前端导入路径..." -ForegroundColor Cyan

# 定义需要修复的文件和替换规则
$files = @(
    "frontend/src/pages/LoginPage.tsx",
    "frontend/src/pages/RegisterPage.tsx", 
    "frontend/src/pages/HomePage.tsx",
    "frontend/src/pages/DocumentPage.tsx",
    "frontend/src/pages/SearchPage.tsx",
    "frontend/src/components/Layout.tsx",
    "frontend/src/components/CreateDocumentModal.tsx",
    "frontend/src/components/Logo.tsx"
)

$replacements = @{
    "from '@/types'" = "from '../types'"
    "from '@/store/authStore'" = "from '../store/authStore'"
    "from '@/store/documentStore'" = "from '../store/documentStore'"
    "from '@/store/categoryStore'" = "from '../store/categoryStore'"
    "from '@/services/api'" = "from '../services/api'"
    "from '@/components/Logo'" = "from './Logo'"
    "from '@/components/Layout'" = "from './Layout'"
    "from '@/components/CreateDocumentModal'" = "from './CreateDocumentModal'"
}

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "修复文件: $file" -ForegroundColor Yellow
        $content = Get-Content $file -Raw
        
        foreach ($pattern in $replacements.Keys) {
            $replacement = $replacements[$pattern]
            $content = $content -replace [regex]::Escape($pattern), $replacement
        }
        
        Set-Content $file $content -NoNewline
        Write-Host "✓ 完成" -ForegroundColor Green
    } else {
        Write-Host "文件不存在: $file" -ForegroundColor Red
    }
}

Write-Host "导入路径修复完成！" -ForegroundColor Green
