# CloudNotes Makefile

.PHONY: help dev setup clean build test docker-up docker-down

# 默认目标
help:
	@echo "CloudNotes 开发命令"
	@echo ""
	@echo "可用命令:"
	@echo "  make setup      - 初始化开发环境"
	@echo "  make dev        - 启动开发服务器"
	@echo "  make build      - 构建生产版本"
	@echo "  make test       - 运行测试"
	@echo "  make clean      - 清理构建文件"
	@echo "  make docker-up  - 启动Docker服务"
	@echo "  make docker-down- 停止Docker服务"

# 初始化开发环境
setup:
	@echo "初始化开发环境..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "已创建.env文件"; fi
	@echo "安装后端依赖..."
	@cd backend && go mod tidy
	@echo "安装前端依赖..."
	@cd frontend && npm install
	@echo "环境初始化完成!"

# 启动开发服务器
dev:
	@echo "启动开发服务器..."
	@make docker-up
	@echo "启动后端服务..."
	@cd backend && go run cmd/server/main.go &
	@echo "启动前端服务..."
	@cd frontend && npm start

# 构建生产版本
build:
	@echo "构建生产版本..."
	@echo "构建前端..."
	@cd frontend && npm run build
	@echo "构建后端..."
	@cd backend && go build -o ../bin/cloudnotes cmd/server/main.go
	@echo "构建完成!"

# 运行测试
test:
	@echo "运行后端测试..."
	@cd backend && go test ./...
	@echo "运行前端测试..."
	@cd frontend && npm test -- --watchAll=false

# 清理构建文件
clean:
	@echo "清理构建文件..."
	@rm -rf frontend/build
	@rm -rf bin
	@echo "清理完成!"

# 启动Docker服务
docker-up:
	@echo "启动Docker服务..."
	@docker-compose up -d postgres minio redis

# 停止Docker服务
docker-down:
	@echo "停止Docker服务..."
	@docker-compose down

# 查看Docker服务状态
docker-status:
	@docker-compose ps

# 查看Docker日志
docker-logs:
	@docker-compose logs -f

# 重置数据库
db-reset:
	@echo "重置数据库..."
	@docker-compose down postgres
	@docker volume rm jiangjiangNote_postgres_data 2>/dev/null || true
	@docker-compose up -d postgres
	@echo "数据库重置完成!"

# 生成API文档
docs:
	@echo "生成API文档..."
	@cd backend && swag init -g cmd/server/main.go

# 代码格式化
fmt:
	@echo "格式化Go代码..."
	@cd backend && go fmt ./...
	@echo "格式化前端代码..."
	@cd frontend && npm run format 2>/dev/null || echo "请配置prettier"

# 代码检查
lint:
	@echo "检查Go代码..."
	@cd backend && golangci-lint run 2>/dev/null || echo "请安装golangci-lint"
	@echo "检查前端代码..."
	@cd frontend && npm run lint 2>/dev/null || echo "请配置eslint"
