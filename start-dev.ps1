# CloudNotes 开发环境启动脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    JiangJiangNote 开发环境启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Go是否安装
try {
    $goVersion = go version 2>$null
    Write-Host "[信息] Go版本: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "[错误] 未检测到Go环境，请先安装Go 1.24+" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查Node.js是否安装
try {
    $nodeVersion = node --version 2>$null
    Write-Host "[信息] Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[错误] 未检测到Node.js环境，请先安装Node.js 18+" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查项目结构
if (-not (Test-Path "backend")) {
    Write-Host "[错误] 未找到backend目录，请确保在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

if (-not (Test-Path "frontend")) {
    Write-Host "[错误] 未找到frontend目录，请确保在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "[信息] 正在检查环境配置..." -ForegroundColor Yellow

# 检查.env文件
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Write-Host "[信息] 复制环境配置文件..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env"
        Write-Host "[提示] 请根据需要修改.env文件中的配置" -ForegroundColor Yellow
    } else {
        Write-Host "[警告] 未找到环境配置文件" -ForegroundColor Yellow
    }
}

# 启动数据库服务
Write-Host "[信息] 启动数据库服务..." -ForegroundColor Yellow
try {
    docker-compose up -d postgres minio redis 2>$null
    Write-Host "[信息] 数据库服务启动成功" -ForegroundColor Green
} catch {
    Write-Host "[警告] Docker服务启动失败，请确保Docker Desktop已启动" -ForegroundColor Yellow
    Write-Host "[提示] 或者手动启动PostgreSQL和MinIO服务" -ForegroundColor Yellow
}

# 安装后端依赖
Write-Host "[信息] 安装后端依赖..." -ForegroundColor Yellow
Set-Location "backend"
try {
    go mod tidy
    Write-Host "[信息] 后端依赖安装完成" -ForegroundColor Green
} catch {
    Write-Host "[错误] 后端依赖安装失败" -ForegroundColor Red
    Set-Location ".."
    Read-Host "按Enter键退出"
    exit 1
}
Set-Location ".."

# 安装前端依赖
Write-Host "[信息] 检查前端依赖..." -ForegroundColor Yellow
Set-Location "frontend"
if (-not (Test-Path "node_modules")) {
    Write-Host "[信息] 安装前端依赖..." -ForegroundColor Yellow
    try {
        npm install
        Write-Host "[信息] 前端依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "[错误] 前端依赖安装失败" -ForegroundColor Red
        Set-Location ".."
        Read-Host "按Enter键退出"
        exit 1
    }
} else {
    Write-Host "[信息] 前端依赖已存在" -ForegroundColor Green
}
Set-Location ".."

Write-Host ""
Write-Host "[信息] 环境准备完成，正在启动服务..." -ForegroundColor Green
Write-Host ""

# 启动后端服务
Write-Host "[信息] 启动后端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; go run cmd/server/main.go" -WindowStyle Normal

# 等待后端服务启动
Start-Sleep -Seconds 3

# 启动前端服务
Write-Host "[信息] 启动前端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm start" -WindowStyle Normal

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    JiangJiangNote 开发服务器已启动" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "后端API服务: http://localhost:8080" -ForegroundColor Green
Write-Host "前端应用:   http://localhost:3000" -ForegroundColor Green
Write-Host ""
Write-Host "数据库管理:" -ForegroundColor Yellow
Write-Host "- PostgreSQL: localhost:5432" -ForegroundColor Yellow
Write-Host "- MinIO控制台: http://localhost:9001" -ForegroundColor Yellow
Write-Host ""
Write-Host "按任意键关闭此窗口..." -ForegroundColor Cyan
Read-Host
