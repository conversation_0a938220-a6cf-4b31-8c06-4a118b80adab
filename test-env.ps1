# JiangJiangNote 环境变量测试脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    JiangJiangNote 环境变量测试" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 .env 文件是否存在
if (Test-Path ".env") {
    Write-Host "[✓] .env 文件存在" -ForegroundColor Green
    Write-Host ""
    
    # 读取并显示 .env 文件内容
    Write-Host "=== .env 文件内容 ===" -ForegroundColor Yellow
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            $key = $matches[1]
            $value = $matches[2]
            
            # 隐藏敏感信息
            if ($key -match "(PASSWORD|SECRET|KEY)") {
                Write-Host "$key=[HIDDEN]" -ForegroundColor Gray
            } else {
                Write-Host "$key=$value" -ForegroundColor White
            }
        } elseif ($_ -match "^#") {
            Write-Host $_ -ForegroundColor DarkGray
        } else {
            Write-Host $_ -ForegroundColor White
        }
    }
    Write-Host "========================" -ForegroundColor Yellow
    Write-Host ""
} else {
    Write-Host "[✗] .env 文件不存在" -ForegroundColor Red
    Write-Host ""
    
    if (Test-Path ".env.example") {
        Write-Host "[信息] 发现 .env.example 文件，正在复制..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env"
        Write-Host "[✓] 已创建 .env 文件" -ForegroundColor Green
        Write-Host ""
    } else {
        Write-Host "[错误] 未找到 .env.example 文件" -ForegroundColor Red
        Write-Host "请手动创建 .env 文件" -ForegroundColor Yellow
        Write-Host ""
    }
}

# 检查 Docker 服务状态
Write-Host "=== Docker 服务状态 ===" -ForegroundColor Yellow
try {
    $dockerStatus = docker-compose ps 2>$null
    if ($dockerStatus) {
        Write-Host "[✓] Docker Compose 服务状态:" -ForegroundColor Green
        docker-compose ps
    } else {
        Write-Host "[!] Docker 服务未启动" -ForegroundColor Yellow
        Write-Host "运行以下命令启动数据库服务:" -ForegroundColor Cyan
        Write-Host "docker-compose up -d postgres minio redis" -ForegroundColor White
    }
} catch {
    Write-Host "[!] Docker 未安装或未启动" -ForegroundColor Yellow
}
Write-Host ""

# 测试数据库连接
Write-Host "=== 数据库连接测试 ===" -ForegroundColor Yellow
$dbHost = if ($env:DB_HOST) { $env:DB_HOST } else { "localhost" }
$dbPort = if ($env:DB_PORT) { $env:DB_PORT } else { "5432" }

try {
    $connection = Test-NetConnection -ComputerName $dbHost -Port $dbPort -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "[✓] PostgreSQL 连接成功 ($dbHost`:$dbPort)" -ForegroundColor Green
    } else {
        Write-Host "[✗] PostgreSQL 连接失败 ($dbHost`:$dbPort)" -ForegroundColor Red
    }
} catch {
    Write-Host "[!] 无法测试数据库连接" -ForegroundColor Yellow
}

# 测试 MinIO 连接
$minioHost = if ($env:MINIO_ENDPOINT) { $env:MINIO_ENDPOINT.Split(':')[0] } else { "localhost" }
$minioPort = if ($env:MINIO_ENDPOINT) { $env:MINIO_ENDPOINT.Split(':')[1] } else { "9000" }

try {
    $connection = Test-NetConnection -ComputerName $minioHost -Port $minioPort -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "[✓] MinIO 连接成功 ($minioHost`:$minioPort)" -ForegroundColor Green
    } else {
        Write-Host "[✗] MinIO 连接失败 ($minioHost`:$minioPort)" -ForegroundColor Red
    }
} catch {
    Write-Host "[!] 无法测试 MinIO 连接" -ForegroundColor Yellow
}
Write-Host ""

# 编译测试
Write-Host "=== 后端编译测试 ===" -ForegroundColor Yellow
if (Test-Path "backend") {
    try {
        Set-Location "backend"
        Write-Host "[信息] 正在编译后端..." -ForegroundColor Cyan
        go build -o ../bin/jiangjiangNote cmd/server/main.go
        
        if (Test-Path "../bin/jiangjiangNote.exe") {
            Write-Host "[✓] 后端编译成功" -ForegroundColor Green
        } else {
            Write-Host "[✗] 后端编译失败" -ForegroundColor Red
        }
        Set-Location ".."
    } catch {
        Write-Host "[✗] 后端编译出错: $($_.Exception.Message)" -ForegroundColor Red
        Set-Location ".."
    }
} else {
    Write-Host "[✗] backend 目录不存在" -ForegroundColor Red
}
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    环境检查完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "如果所有检查都通过，可以运行:" -ForegroundColor Green
Write-Host ".\start-dev.ps1" -ForegroundColor White
Write-Host ""

Read-Host "按Enter键退出"
