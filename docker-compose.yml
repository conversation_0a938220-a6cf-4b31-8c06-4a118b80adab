version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: cloudnotes_postgres
    environment:
      POSTGRES_DB: cloudnotes
      POSTGRES_USER: cloudnotes
      POSTGRES_PASSWORD: cloudnotes123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - cloudnotes_network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: cloudnotes_minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - cloudnotes_network

  # Redis缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: cloudnotes_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cloudnotes_network

volumes:
  postgres_data:
  minio_data:
  redis_data:

networks:
  cloudnotes_network:
    driver: bridge
