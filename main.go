package main

import (
	"log"
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	// 检查是否在项目根目录
	if _, err := os.Stat("backend"); err != nil {
		log.Fatal("请在项目根目录运行此程序")
	}

	// 启动后端服务
	log.Println("启动后端服务...")
	backendCmd := exec.Command("go", "run", "backend/cmd/server/main.go")
	backendCmd.Dir = "."
	backendCmd.Stdout = os.Stdout
	backendCmd.Stderr = os.Stderr

	if err := backendCmd.Start(); err != nil {
		log.Fatal("启动后端服务失败:", err)
	}

	log.Printf("CloudNotes 开发服务器已启动")
	log.Printf("后端API: http://localhost:8080")
	log.Printf("前端应用: http://localhost:3000")
	log.Printf("按 Ctrl+C 停止服务")

	// 等待进程结束
	backendCmd.Wait()
}