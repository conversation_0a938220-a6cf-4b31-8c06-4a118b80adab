# CloudNotes 开发指南

## 项目结构

```
jiangjiangNote/
├── backend/                 # 后端Go服务
│   ├── cmd/server/         # 程序入口
│   ├── internal/           # 内部包
│   │   ├── api/           # API路由和处理器
│   │   ├── config/        # 配置管理
│   │   ├── database/      # 数据库连接
│   │   ├── models/        # 数据模型
│   │   └── utils/         # 工具函数
│   └── go.mod
├── frontend/               # 前端React应用
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── store/         # 状态管理
│   │   └── types/         # TypeScript类型
│   └── package.json
├── docker-compose.yml      # 开发环境
├── .env.example           # 环境变量示例
└── README.md
```

## 开发环境搭建

### 1. 环境要求

- Go 1.24+
- Node.js 18+
- PostgreSQL 15+
- Docker Desktop (可选)

### 2. 克隆项目

```bash
git clone <repository-url>
cd jiangjiangNote
```

### 3. 配置环境变量

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
# 根据你的实际环境修改数据库连接信息等
```

### 4. 启动数据库服务

使用Docker启动PostgreSQL和MinIO：

```bash
docker-compose up -d postgres minio
```

或者手动安装并启动PostgreSQL和MinIO服务。

### 5. 启动后端服务

```bash
cd backend
go mod tidy
go run cmd/server/main.go
```

后端服务将在 http://localhost:8080 启动

### 6. 启动前端服务

```bash
cd frontend
npm install
npm start
```

前端应用将在 http://localhost:3000 启动

## API文档

### 认证接口

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户信息

### 文档管理

- `GET /api/documents` - 获取文档列表
- `GET /api/documents/:id` - 获取文档详情
- `POST /api/documents` - 创建文档
- `PUT /api/documents/:id` - 更新文档
- `DELETE /api/documents/:id` - 删除文档

### 分类管理

- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类
- `PUT /api/categories/:id` - 更新分类
- `DELETE /api/categories/:id` - 删除分类

## 开发规范

### 后端规范

1. 遵循Go官方代码规范
2. 使用GORM进行数据库操作
3. 统一的错误处理和响应格式
4. 使用JWT进行身份认证

### 前端规范

1. 使用TypeScript进行类型检查
2. 遵循React Hooks最佳实践
3. 使用Ant Design组件库
4. 统一的API调用和错误处理

### Git提交规范

- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

## 部署指南

### 生产环境部署

1. 构建前端应用：
```bash
cd frontend
npm run build
```

2. 构建后端应用：
```bash
cd backend
go build -o cloudnotes cmd/server/main.go
```

3. 配置生产环境变量
4. 启动服务

### Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 常见问题

### 1. 数据库连接失败

检查PostgreSQL服务是否启动，以及连接配置是否正确。

### 2. 前端无法访问后端API

检查后端服务是否启动，以及CORS配置是否正确。

### 3. JWT认证失败

检查JWT密钥配置，确保前后端使用相同的密钥。

## 开发工具推荐

- **后端开发**: GoLand 或 VS Code + Go插件
- **前端开发**: VS Code + React插件
- **数据库管理**: DBeaver 或 pgAdmin
- **API测试**: Postman 或 Insomnia
- **版本控制**: Git + GitHub Desktop

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

欢迎提交Issue和Pull Request！
