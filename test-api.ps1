# JiangJiangNote API 测试脚本

Write-Host "JiangJiangNote API 测试" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

$baseUrl = "http://localhost:8080"

# 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get
    Write-Host "✅ 健康检查成功: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试用户注册
Write-Host "`n2. 测试用户注册..." -ForegroundColor Yellow
$registerData = @{
    username = "testuser"
    email = "<EMAIL>"
    password = "123456"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method Post -Body $registerData -ContentType "application/json"
    $token = $response.token
    Write-Host "✅ 用户注册成功" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 用户可能已存在，尝试登录..." -ForegroundColor Yellow
    
    # 尝试登录
    $loginData = @{
        email = "<EMAIL>"
        password = "123456"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -Body $loginData -ContentType "application/json"
        $token = $response.token
        Write-Host "✅ 用户登录成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# 设置认证头
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# 测试获取用户信息
Write-Host "`n3. 测试获取用户信息..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/profile" -Method Get -Headers $headers
    Write-Host "✅ 获取用户信息成功: $($response.username)" -ForegroundColor Green
} catch {
    Write-Host "❌ 获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试创建分类
Write-Host "`n4. 测试创建分类..." -ForegroundColor Yellow
$categoryData = @{
    name = "测试分类"
    icon = "test"
    color = "#1890ff"
    sort_order = 1
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/categories" -Method Post -Body $categoryData -Headers $headers
    $categoryId = $response.id
    Write-Host "✅ 创建分类成功: $($response.name)" -ForegroundColor Green
} catch {
    Write-Host "❌ 创建分类失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试获取分类列表
Write-Host "`n5. 测试获取分类列表..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/categories" -Method Get -Headers $headers
    Write-Host "✅ 获取分类列表成功，共 $($response.Count) 个分类" -ForegroundColor Green
} catch {
    Write-Host "❌ 获取分类列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试创建文档
Write-Host "`n6. 测试创建文档..." -ForegroundColor Yellow
$documentData = @{
    title = "测试文档"
    content = "# 这是一个测试文档`n`n这是文档内容。"
    content_type = "markdown"
    category_id = $categoryId
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/documents" -Method Post -Body $documentData -Headers $headers
    $documentId = $response.id
    Write-Host "✅ 创建文档成功: $($response.title)" -ForegroundColor Green
} catch {
    Write-Host "❌ 创建文档失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试获取文档列表
Write-Host "`n7. 测试获取文档列表..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/documents" -Method Get -Headers $headers
    if ($response.documents) {
        Write-Host "✅ 获取文档列表成功，共 $($response.documents.Count) 个文档" -ForegroundColor Green
    } else {
        Write-Host "✅ 获取文档列表成功，共 $($response.Count) 个文档" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ 获取文档列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试搜索
Write-Host "`n8. 测试搜索功能..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/search?q=测试" -Method Get -Headers $headers
    Write-Host "✅ 搜索功能正常" -ForegroundColor Green
} catch {
    Write-Host "❌ 搜索功能失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API 测试完成！" -ForegroundColor Cyan
Write-Host "现在可以启动前端应用进行完整测试。" -ForegroundColor Green
