# JiangJiangNote 项目设置脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    JiangJiangNote 项目设置脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Go是否安装
try {
    $goVersion = go version 2>$null
    Write-Host "[信息] Go版本: $goVersion" -ForegroundColor Green
} catch {
    Write-Host "[错误] 未检测到Go环境，请先安装Go 1.24+" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查Node.js是否安装
try {
    $nodeVersion = node --version 2>$null
    Write-Host "[信息] Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[错误] 未检测到Node.js环境，请先安装Node.js 18+" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 创建项目目录
Write-Host "[信息] 创建项目目录..." -ForegroundColor Yellow
if (Test-Path "jiangjiangNote") {
    Write-Host "[警告] 项目目录已存在，将覆盖现有文件" -ForegroundColor Yellow
    Remove-Item -Recurse -Force "jiangjiangNote" -ErrorAction SilentlyContinue
}

New-Item -ItemType Directory -Path "jiangjiangNote" -Force | Out-Null
Set-Location "jiangjiangNote"

# 创建后端目录结构
Write-Host "[信息] 创建后端目录结构..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "backend" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/cmd/server" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/api/handlers" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/api/middleware" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/api/routes" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/config" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/database" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/models" -Force | Out-Null
New-Item -ItemType Directory -Path "backend/internal/utils" -Force | Out-Null

# 创建前端目录结构
Write-Host "[信息] 创建前端目录结构..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "frontend" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/public" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/src" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/src/components" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/src/pages" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/src/services" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/src/store" -Force | Out-Null
New-Item -ItemType Directory -Path "frontend/src/types" -Force | Out-Null

Write-Host "[信息] 项目目录结构创建完成" -ForegroundColor Green
Write-Host ""
Write-Host "接下来请按照以下步骤操作：" -ForegroundColor Cyan
Write-Host "1. 将所有项目文件复制到 jiangjiangNote 目录中" -ForegroundColor Yellow
Write-Host "2. 运行 .\start-dev.ps1 启动项目" -ForegroundColor Yellow
Write-Host ""
Write-Host "项目目录已创建在: $(Get-Location)" -ForegroundColor Green

Read-Host "按Enter键退出"
