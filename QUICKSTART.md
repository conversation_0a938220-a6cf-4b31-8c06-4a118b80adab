# JiangJiangNote 快速启动指南

## 🚀 一键启动

### Windows 用户

1. **使用 PowerShell 脚本（推荐）**
   ```powershell
   .\start-dev.ps1
   ```

2. **使用批处理脚本**
   ```cmd
   start-dev.bat
   ```

### 手动启动

如果自动脚本遇到问题，可以手动启动：

1. **配置环境变量**
   ```bash
   cp .env.example .env
   # 根据需要修改配置
   ```

2. **启动数据库服务**
   ```bash
   docker-compose up -d postgres minio redis
   ```

3. **启动后端服务**
   ```bash
   cd backend
   go mod tidy
   go run cmd/server/main.go
   ```

4. **启动前端服务**（新开一个终端）
   ```bash
   cd frontend
   npm install
   npm start
   ```

## 📋 环境要求

- **Go**: 1.24+
- **Node.js**: 18+
- **Docker Desktop**: 最新版本（用于数据库）

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **API健康检查**: http://localhost:8080/health
- **MinIO控制台**: http://localhost:9001

## 👤 默认账户

系统会自动创建一个演示账户：

- **邮箱**: <EMAIL>
- **密码**: 123456

## ⚙️ 环境变量配置

### 主要配置项

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SERVER_PORT` | 8080 | 后端服务端口 |
| `GIN_MODE` | debug | Gin运行模式 |
| `DB_HOST` | localhost | 数据库主机 |
| `DB_PORT` | 5432 | 数据库端口 |
| `DB_USER` | cloudnotes | 数据库用户名 |
| `DB_PASSWORD` | cloudnotes123 | 数据库密码 |
| `DB_NAME` | cloudnotes | 数据库名称 |
| `JWT_SECRET` | jiangjiang-note-super-secret-jwt-key-2024 | JWT密钥 |
| `JWT_EXPIRE_HOUR` | 24 | JWT过期时间（小时） |
| `MINIO_ENDPOINT` | localhost:9000 | MinIO服务地址 |
| `MINIO_ACCESS_KEY` | minioadmin | MinIO访问密钥 |
| `MINIO_SECRET_KEY` | minioadmin123 | MinIO秘密密钥 |
| `MINIO_BUCKET` | jiangjiangNote | MinIO存储桶名称 |

### 环境变量验证

运行环境检查脚本：
```powershell
.\test-env.ps1
```

## 🧪 测试API

运行API测试脚本验证后端服务：

```powershell
.\test-api.ps1
```

## 📁 项目结构

```
jiangjiangNote/
├── backend/                 # Go后端服务
│   ├── cmd/server/         # 程序入口
│   ├── internal/           # 内部包
│   └── go.mod
├── frontend/               # React前端应用
│   ├── src/
│   └── package.json
├── docker-compose.yml      # 数据库服务
├── start-dev.ps1          # PowerShell启动脚本
├── start-dev.bat          # 批处理启动脚本
└── test-api.ps1           # API测试脚本
```

## 🎯 主要功能

- ✅ **用户认证**: 注册、登录、个人资料管理
- ✅ **文档管理**: 创建、编辑、删除、查看文档
- ✅ **分类系统**: 文档分类和层级管理
- ✅ **标签系统**: 文档标签和筛选
- ✅ **搜索功能**: 全文搜索文档、分类、标签
- ✅ **收藏功能**: 收藏重要文档
- ✅ **最近访问**: 快速访问最近编辑的文档
- ✅ **Markdown支持**: 实时预览和编辑
- ✅ **深色主题**: 现代化的紫色主题UI

## 🛠️ 开发工具

- **后端开发**: GoLand 或 VS Code + Go插件
- **前端开发**: VS Code + React插件
- **数据库管理**: DBeaver 或 pgAdmin
- **API测试**: Postman 或内置测试脚本

## 🐛 常见问题

### 1. 端口被占用
如果遇到端口被占用的错误：
- 后端端口 8080
- 前端端口 3000
- PostgreSQL 端口 5432
- MinIO 端口 9000, 9001

可以修改 `.env` 文件中的端口配置。

### 2. Docker 启动失败
确保 Docker Desktop 已启动并运行正常。

### 3. Go 模块下载慢
可以设置 Go 代理：
```bash
go env -w GOPROXY=https://goproxy.cn,direct
```

### 4. npm 安装慢
可以使用国内镜像：
```bash
npm config set registry https://registry.npmmirror.com
```

## 📞 获取帮助

如果遇到问题：

1. 检查所有服务是否正常启动
2. 查看终端错误信息
3. 运行 `.\test-api.ps1` 测试后端API
4. 检查 `.env` 配置文件

## 🎉 开始使用

1. 启动服务后访问 http://localhost:3000
2. 使用默认账户登录或注册新账户
3. 开始创建你的第一个文档！

---

**祝你使用愉快！** 🎊
